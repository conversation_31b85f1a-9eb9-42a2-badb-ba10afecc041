<template>
  <div class="timeout-warning-overlay">
    <!-- 警告背景遮罩 -->
    <div class="warning-backdrop"></div>
    
    <!-- 警告内容 -->
    <div class="warning-content">
      <!-- 警告图标 -->
      <div class="warning-icon">
        <div class="icon-circle">
          <svg class="warning-svg" viewBox="0 0 24 24" fill="none">
            <path d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>
      
      <!-- 警告信息 -->
      <div class="warning-info">
        <h3 class="warning-title">动作即将超时</h3>
        <p class="warning-message">{{ warningData.message }}</p>
        
        <!-- 倒计时显示 -->
        <div class="countdown-display">
          <div class="countdown-circle">
            <svg class="countdown-svg" viewBox="0 0 100 100">
              <circle 
                class="countdown-bg" 
                cx="50" 
                cy="50" 
                r="45"
                fill="none"
                stroke="rgba(255,255,255,0.2)"
                stroke-width="8"
              />
              <circle 
                class="countdown-progress" 
                cx="50" 
                cy="50" 
                r="45"
                fill="none"
                stroke="currentColor"
                stroke-width="8"
                stroke-linecap="round"
                :stroke-dasharray="circumference"
                :stroke-dashoffset="progressOffset"
                transform="rotate(-90 50 50)"
              />
            </svg>
            <div class="countdown-text">
              <span class="countdown-number">{{ remainingTime }}</span>
              <span class="countdown-unit">秒</span>
            </div>
          </div>
        </div>
        
        <!-- 提示信息 -->
        <div class="warning-tips">
          <p class="tip-text">请继续尝试完成动作</p>
          <p class="tip-subtext">系统将在时间结束后自动跳过此动作</p>
        </div>
      </div>
    </div>
    
    <!-- 进度条 -->
    <div class="warning-progress">
      <div class="progress-bar">
        <div 
          class="progress-fill" 
          :style="{ width: progressPercentage + '%' }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  warningData: {
    type: Object,
    required: true,
    default: () => ({
      type: 'timeout_warning',
      remainingTime: 30,
      message: '动作即将超时，请继续尝试'
    })
  },
  autoHide: {
    type: Boolean,
    default: false
  },
  hideDelay: {
    type: Number,
    default: 5000
  }
})

const emit = defineEmits(['hide', 'timeout'])

// 响应式数据
const remainingTime = ref(props.warningData.remainingTime || 30)
const isVisible = ref(true)
const countdownTimer = ref(null)

// 计算属性
const circumference = 2 * Math.PI * 45

const progressPercentage = computed(() => {
  const total = props.warningData.remainingTime || 30
  return ((total - remainingTime.value) / total) * 100
})

const progressOffset = computed(() => {
  const progress = progressPercentage.value / 100
  return circumference * (1 - progress)
})

// 方法
const startCountdown = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
  
  countdownTimer.value = setInterval(() => {
    remainingTime.value--
    
    if (remainingTime.value <= 0) {
      clearInterval(countdownTimer.value)
      handleTimeout()
    }
  }, 1000)
}

const handleTimeout = () => {
  console.log('[TimeoutWarning] 倒计时结束')
  emit('timeout')
  
  if (props.autoHide) {
    hideWarning()
  }
}

const hideWarning = () => {
  isVisible.value = false
  emit('hide')
}

const cleanup = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
}

// 生命周期
onMounted(() => {
  console.log('[TimeoutWarning] 警告组件挂载，开始倒计时')
  startCountdown()
  
  // 自动隐藏
  if (props.autoHide && props.hideDelay > 0) {
    setTimeout(() => {
      hideWarning()
    }, props.hideDelay)
  }
})

onUnmounted(() => {
  console.log('[TimeoutWarning] 警告组件卸载，清理定时器')
  cleanup()
})
</script>

<style scoped>
.timeout-warning-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.warning-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
}

.warning-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.95), rgba(255, 152, 0, 0.95));
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(255, 193, 7, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  max-width: 400px;
  text-align: center;
  animation: warningPulse 2s ease-in-out infinite;
}

.warning-icon {
  margin-bottom: 1.5rem;
}

.icon-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: iconBounce 1s ease-in-out infinite;
}

.warning-svg {
  width: 40px;
  height: 40px;
  color: white;
}

.warning-info {
  color: white;
}

.warning-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.warning-message {
  font-size: 1rem;
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.countdown-display {
  margin-bottom: 1.5rem;
}

.countdown-circle {
  position: relative;
  width: 100px;
  height: 100px;
  margin: 0 auto;
}

.countdown-svg {
  width: 100%;
  height: 100%;
  color: white;
}

.countdown-progress {
  transition: stroke-dashoffset 1s linear;
}

.countdown-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.countdown-number {
  font-size: 1.8rem;
  font-weight: bold;
  line-height: 1;
}

.countdown-unit {
  font-size: 0.8rem;
  opacity: 0.8;
}

.warning-tips {
  text-align: center;
}

.tip-text {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.tip-subtext {
  font-size: 0.85rem;
  opacity: 0.8;
}

.warning-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.progress-bar {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0 0 20px 20px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.6));
  transition: width 1s linear;
}

/* 动画 */
@keyframes warningPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 20px 40px rgba(255, 193, 7, 0.3);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 25px 50px rgba(255, 193, 7, 0.4);
  }
}

@keyframes iconBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .warning-content {
    margin: 1rem;
    padding: 1.5rem;
    max-width: calc(100vw - 2rem);
  }
  
  .warning-title {
    font-size: 1.25rem;
  }
  
  .countdown-circle {
    width: 80px;
    height: 80px;
  }
  
  .countdown-number {
    font-size: 1.5rem;
  }
}
</style>