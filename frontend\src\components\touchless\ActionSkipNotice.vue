<template>
  <div class="skip-notice-overlay" v-if="isVisible">
    <!-- 背景遮罩 -->
    <div class="notice-backdrop"></div>
    
    <!-- 通知内容 -->
    <div class="notice-content">
      <!-- 跳过图标 -->
      <div class="skip-icon">
        <div class="icon-wrapper">
          <svg class="skip-svg" viewBox="0 0 24 24" fill="none">
            <path d="M4.5 12.75l6 6 9-13.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>
      
      <!-- 跳过信息 -->
      <div class="skip-info">
        <h3 class="skip-title">动作已跳过</h3>
        <p class="skip-action-name">{{ skipData.action?.action_name || '未知动作' }}</p>
        <p class="skip-reason">{{ getSkipReasonText(skipData.reason) }}</p>
        
        <!-- 跳过详情 -->
        <div class="skip-details" v-if="showDetails">
          <div class="detail-item">
            <span class="detail-label">尝试时长:</span>
            <span class="detail-value">{{ formatDuration(skipData.elapsedTime) }}</span>
          </div>
          <div class="detail-item" v-if="skipData.stagnationTime">
            <span class="detail-label">停滞时长:</span>
            <span class="detail-value">{{ formatDuration(skipData.stagnationTime) }}</span>
          </div>
        </div>
        
        <!-- 下一步提示 -->
        <div class="next-action-info">
          <p class="next-text">正在准备下一个动作...</p>
          <div class="loading-dots">
            <span class="dot"></span>
            <span class="dot"></span>
            <span class="dot"></span>
          </div>
        </div>
      </div>
      
      <!-- 进度指示器 -->
      <div class="progress-indicator">
        <div class="progress-circle">
          <svg class="progress-svg" viewBox="0 0 100 100">
            <circle 
              class="progress-bg" 
              cx="50" 
              cy="50" 
              r="45"
              fill="none"
              stroke="rgba(255,255,255,0.2)"
              stroke-width="6"
            />
            <circle 
              class="progress-fill" 
              cx="50" 
              cy="50" 
              r="45"
              fill="none"
              stroke="currentColor"
              stroke-width="6"
              stroke-linecap="round"
              :stroke-dasharray="circumference"
              :stroke-dashoffset="progressOffset"
              transform="rotate(-90 50 50)"
            />
          </svg>
          <div class="progress-text">
            <span class="progress-number">{{ countdown }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  skipData: {
    type: Object,
    required: true,
    default: () => ({
      action: { action_name: '未知动作' },
      reason: 'timeout',
      elapsedTime: 0,
      stagnationTime: 0
    })
  },
  displayDuration: {
    type: Number,
    default: 4000 // 4秒显示时间
  },
  showDetails: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['hide', 'complete'])

// 响应式数据
const isVisible = ref(true)
const countdown = ref(Math.ceil(props.displayDuration / 1000))
const countdownTimer = ref(null)
const hideTimer = ref(null)

// 计算属性
const circumference = 2 * Math.PI * 45

const progressOffset = computed(() => {
  const totalSeconds = Math.ceil(props.displayDuration / 1000)
  const progress = (totalSeconds - countdown.value) / totalSeconds
  return circumference * (1 - progress)
})

// 方法
const getSkipReasonText = (reason) => {
  const reasonMap = {
    'timeout': '动作执行超时',
    'stagnation': '动作进展停滞',
    'manual': '手动跳过',
    'repeated_failure': '多次尝试失败',
    'emergency_stop': '紧急停止',
    'identity_lost': '身份验证失败'
  }
  
  return reasonMap[reason] || '系统自动跳过'
}

const formatDuration = (milliseconds) => {
  if (!milliseconds) return '0秒'
  
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  
  if (minutes > 0) {
    return `${minutes}分${remainingSeconds}秒`
  }
  return `${remainingSeconds}秒`
}

const startCountdown = () => {
  countdownTimer.value = setInterval(() => {
    countdown.value--
    
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value)
    }
  }, 1000)
}

const hideNotice = () => {
  isVisible.value = false
  emit('hide')
  
  // 延迟触发完成事件，让动画播放完毕
  setTimeout(() => {
    emit('complete')
  }, 300)
}

const cleanup = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
  
  if (hideTimer.value) {
    clearTimeout(hideTimer.value)
    hideTimer.value = null
  }
}

// 生命周期
onMounted(() => {
  console.log('[ActionSkipNotice] 跳过通知显示:', props.skipData)
  
  startCountdown()
  
  // 设置自动隐藏
  hideTimer.value = setTimeout(() => {
    hideNotice()
  }, props.displayDuration)
})

onUnmounted(() => {
  cleanup()
})
</script>

<style scoped>
.skip-notice-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  animation: fadeIn 0.3s ease-out;
}

.notice-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(2px);
}

.notice-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.95), rgba(22, 163, 74, 0.95));
  border-radius: 24px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(34, 197, 94, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  max-width: 420px;
  text-align: center;
  animation: slideInUp 0.4s ease-out;
}

.skip-icon {
  margin-bottom: 1.5rem;
}

.icon-wrapper {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: checkmarkScale 0.6s ease-out;
}

.skip-svg {
  width: 40px;
  height: 40px;
  color: white;
}

.skip-info {
  color: white;
  margin-bottom: 1.5rem;
}

.skip-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.skip-action-name {
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.skip-reason {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-bottom: 1rem;
}

.skip-details {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 0.85rem;
  opacity: 0.8;
}

.detail-value {
  font-size: 0.85rem;
  font-weight: 500;
}

.next-action-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.next-text {
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.loading-dots {
  display: flex;
  gap: 0.3rem;
}

.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  animation: dotPulse 1.4s ease-in-out infinite;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

.progress-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.progress-circle {
  position: relative;
  width: 50px;
  height: 50px;
}

.progress-svg {
  width: 100%;
  height: 100%;
  color: rgba(255, 255, 255, 0.8);
}

.progress-fill {
  transition: stroke-dashoffset 1s linear;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.progress-number {
  font-size: 0.8rem;
  font-weight: bold;
  color: white;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes checkmarkScale {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes dotPulse {
  0%, 80%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  40% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .notice-content {
    margin: 1rem;
    padding: 1.5rem;
    max-width: calc(100vw - 2rem);
  }
  
  .skip-title {
    font-size: 1.25rem;
  }
  
  .progress-indicator {
    top: 0.5rem;
    right: 0.5rem;
  }
  
  .progress-circle {
    width: 40px;
    height: 40px;
  }
  
  .progress-number {
    font-size: 0.7rem;
  }
}
</style>