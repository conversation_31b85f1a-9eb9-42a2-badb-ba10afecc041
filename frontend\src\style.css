@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* html, body {
  height: 100%;
  font-family: 'Inter', system-ui, sans-serif;
}

#app {
  height: 100vh;
  overflow: hidden;
} */

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 工具类 */
.flex-center {
  @apply flex items-center justify-center;
}

.text-gradient {
  @apply bg-gradient-to-r from-primary-500 to-primary-700 bg-clip-text text-transparent;
}

.card-shadow {
  @apply shadow-lg shadow-gray-200/50;
}

.transition-smooth {
  @apply transition-all duration-300 ease-in-out;
}

/* 庆祝动画相关样式 */
.celebration-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9998;
}

/* 动画性能优化 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
}

/* 庆祝动画的全局关键帧 */
@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
}

@keyframes float-up {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-100px) scale(0.5);
  }
}

@keyframes rainbow-text {
  0% { color: #ff6b6b; }
  16.66% { color: #4ecdc4; }
  33.33% { color: #45b7d1; }
  50% { color: #96ceb4; }
  66.66% { color: #feca57; }
  83.33% { color: #ff9ff3; }
  100% { color: #54a0ff; }
}

/* 响应式动画调整 */
@media (prefers-reduced-motion: reduce) {
  .celebration-overlay * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
