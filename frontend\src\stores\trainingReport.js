/**
 * 训练报告存储管理器
 * 负责收集、处理和管理详细的训练报告数据
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ACTION_ADVICE_TEMPLATES, OVERALL_ASSESSMENT_LEVELS } from '@/types/trainingReport'

export const useTrainingReportStore = defineStore('trainingReport', () => {
  // 当前训练会话的详细记录
  const currentSessionReport = ref(null)
  const actionDetailedRecords = ref([])
  const sessionStartTime = ref(null)
  // 当前正在记录的动作
  const currentActionRecord = ref(null)
  
  /**
   * 开始新的训练会话报告
   */
  const startSessionReport = (patientInfo, sessionId) => {
    sessionStartTime.value = new Date().toISOString()
    
    currentSessionReport.value = {
      session_id: sessionId,
      patient_info: {
        patient_id: patientInfo.patient_id,
        patient_name: patientInfo.patient_name || `患者${patientInfo.patient_id}`,
        session_date: new Date().toLocaleDateString('zh-CN')
      },
      session_info: {
        start_time: sessionStartTime.value,
        end_time: null,
        total_duration: 0,
        total_actions: 0,
        completed_actions: 0
      },
      detailed_actions: [],
      statistics: {
        average_score: 0,
        completion_rate: 0,
        total_stages_completed: 0,
        performance_trends: []
      }
    }
    
    actionDetailedRecords.value = []
    console.log('[TrainingReport] 开始新的训练会话报告')
  }
  
  /**
   * 开始记录新的动作
   */
  const startActionRecord = (actionInfo) => {
    // 检查是否已经在记录同一个动作
    if (currentActionRecord.value &&
        currentActionRecord.value.action_id === actionInfo.action_id &&
        currentActionRecord.value.status === 'in_progress') {
      console.log(`[TrainingReport] 动作 ${actionInfo.action_name} 已在记录中，跳过重复开始`)
      return
    }

    const actionStartTime = new Date().toISOString()

    currentActionRecord.value = {
      action_id: actionInfo.action_id,
      action_type: actionInfo.action_type,
      action_name: actionInfo.action_name,
      side: actionInfo.side,
      difficulty_level: actionInfo.difficulty_level,
      start_time: actionStartTime,
      end_time: null,
      duration_seconds: 0,
      final_score: 0,
      status: 'in_progress',

      // 阶段评分详情
      stage_scores: [],

      // 反馈时间线
      feedback_timeline: [],

      // 性能指标
      performance_metrics: {
        max_score_reached: 0,
        min_score_reached: 100,
        score_variance: 0,
        stability_index: 0,
        completion_efficiency: 0
      },

      // 动作建议（稍后生成）
      advice: null
    }

    console.log(`[TrainingReport] 开始记录动作: ${actionInfo.action_name} (开始时间: ${actionStartTime})`)
  }
  
  /**
   * 记录动作阶段评分
   */
  const recordStageScore = (stageName, score, maxScore, feedback = '') => {
    if (!currentActionRecord.value) return
    
    const stageScore = {
      stage: stageName,
      score: score,
      maxScore: maxScore,
      percentage: Math.round((score / maxScore) * 100),
      timestamp: new Date().toISOString(),
      feedback: feedback
    }
    
    currentActionRecord.value.stage_scores.push(stageScore)
    console.log(`[TrainingReport] 记录阶段评分: ${stageName} - ${score}/${maxScore}`)
  }
  
  /**
   * 记录实时反馈
   */
  const recordFeedback = (feedback, score, state) => {
    if (!currentActionRecord.value) return
    
    const feedbackRecord = {
      timestamp: new Date().toISOString(),
      feedback: feedback,
      score: score,
      state: state
    }
    
    currentActionRecord.value.feedback_timeline.push(feedbackRecord)
    
    // 更新性能指标
    const metrics = currentActionRecord.value.performance_metrics
    metrics.max_score_reached = Math.max(metrics.max_score_reached, score)
    metrics.min_score_reached = Math.min(metrics.min_score_reached, score)
  }
  
  /**
   * 生成动作建议
   */
  const generateActionAdvice = (actionType, finalScore) => {
    const templates = ACTION_ADVICE_TEMPLATES[actionType]
    if (!templates) return null
    
    let category = 'needs_improvement'
    if (finalScore >= 90) category = 'excellent'
    else if (finalScore >= 75) category = 'good'
    else if (finalScore >= 60) category = 'needs_improvement'
    else category = 'poor'
    
    const template = templates[category]
    return {
      category: category,
      overall: template.overall,
      strengths: [...template.strengths],
      improvements: [...template.improvements],
      tips: [...template.tips]
    }
  }
  
  /**
   * 完成当前动作记录
   */
  const completeActionRecord = (finalScore, status = 'completed') => {
    if (!currentActionRecord.value) return

    const endTime = new Date().toISOString()
    const startTime = new Date(currentActionRecord.value.start_time)
    const duration = Math.round((new Date(endTime) - startTime) / 1000)

    console.log(`[TrainingReport] 动作完成时间计算:`)
    console.log(`  动作: ${currentActionRecord.value.action_name}`)
    console.log(`  开始时间: ${currentActionRecord.value.start_time}`)
    console.log(`  结束时间: ${endTime}`)
    console.log(`  持续时间: ${duration}秒`)

    // 更新基本信息
    currentActionRecord.value.end_time = endTime
    currentActionRecord.value.duration_seconds = duration
    currentActionRecord.value.final_score = finalScore
    currentActionRecord.value.status = status
    
    // 计算性能指标
    const metrics = currentActionRecord.value.performance_metrics
    const scores = currentActionRecord.value.feedback_timeline.map(f => f.score)
    if (scores.length > 0) {
      const avgScore = scores.reduce((sum, s) => sum + s, 0) / scores.length
      metrics.score_variance = Math.round(scores.reduce((sum, s) => sum + Math.pow(s - avgScore, 2), 0) / scores.length)
      metrics.stability_index = Math.max(0, 100 - metrics.score_variance)
      metrics.completion_efficiency = Math.round((finalScore / duration) * 10) // 分数/时间的效率指标
    }
    
    // 生成动作建议
    currentActionRecord.value.advice = generateActionAdvice(
      currentActionRecord.value.action_type, 
      finalScore
    )
    
    // 添加到详细记录列表
    actionDetailedRecords.value.push({ ...currentActionRecord.value })
    
    console.log(`[TrainingReport] 完成动作记录: ${currentActionRecord.value.action_name}, 得分: ${finalScore}`)
    
    // 重置当前记录
    currentActionRecord.value = null
  }
  
  /**
   * 生成总体评价
   */
  const generateOverallAssessment = () => {
    if (actionDetailedRecords.value.length === 0) return null
    
    const completedActions = actionDetailedRecords.value.filter(a => a.status === 'completed')
    const totalScore = completedActions.reduce((sum, a) => sum + a.final_score, 0)
    const averageScore = completedActions.length > 0 ? Math.round(totalScore / completedActions.length) : 0
    
    // 确定总体水平
    let level = 'needs_improvement'
    for (const [levelKey, levelInfo] of Object.entries(OVERALL_ASSESSMENT_LEVELS)) {
      if (averageScore >= levelInfo.min) {
        level = levelKey
        break
      }
    }
    
    // 生成成就和关注领域
    const achievements = []
    const focusAreas = []
    const nextGoals = []
    
    completedActions.forEach(action => {
      if (action.final_score >= 80) {
        achievements.push(`${action.action_name}表现优秀`)
      } else if (action.final_score < 60) {
        focusAreas.push(`${action.action_name}需要重点练习`)
        nextGoals.push(`提高${action.action_name}的完成质量`)
      }
    })
    
    // 通用建议
    if (achievements.length === 0) {
      achievements.push('积极参与康复训练')
    }
    if (nextGoals.length === 0) {
      nextGoals.push('继续保持训练的连续性')
    }
    
    return {
      level: level,
      overall_score: averageScore,
      summary: generateAssessmentSummary(level, averageScore, completedActions.length),
      achievements: achievements,
      focus_areas: focusAreas,
      next_goals: nextGoals
    }
  }
  
  /**
   * 生成评价总结
   */
  const generateAssessmentSummary = (level, score, actionCount) => {
    const levelLabel = OVERALL_ASSESSMENT_LEVELS[level].label
    return `本次训练完成了${actionCount}个动作，平均得分${score}分，总体表现${levelLabel}。${
      level === 'excellent' ? '继续保持这种优秀的表现！' :
      level === 'good' ? '表现良好，继续努力提升！' :
      level === 'fair' ? '有一定进步，需要持续练习。' :
      '需要加强练习，建议寻求专业指导。'
    }`
  }
  
  /**
   * 完成训练会话报告
   */
  const completeSessionReport = () => {
    if (!currentSessionReport.value) return null
    
    const endTime = new Date().toISOString()
    const duration = Math.round((new Date(endTime) - new Date(sessionStartTime.value)) / 1000)
    
    // 更新会话信息
    currentSessionReport.value.session_info.end_time = endTime
    currentSessionReport.value.session_info.total_duration = duration
    currentSessionReport.value.session_info.total_actions = actionDetailedRecords.value.length
    currentSessionReport.value.session_info.completed_actions = actionDetailedRecords.value.filter(a => a.status === 'completed').length
    
    // 更新详细动作记录
    currentSessionReport.value.detailed_actions = [...actionDetailedRecords.value]
    
    // 计算统计数据
    const completedActions = actionDetailedRecords.value.filter(a => a.status === 'completed')
    currentSessionReport.value.statistics = {
      average_score: completedActions.length > 0 ? 
        Math.round(completedActions.reduce((sum, a) => sum + a.final_score, 0) / completedActions.length) : 0,
      completion_rate: actionDetailedRecords.value.length > 0 ? 
        Math.round((completedActions.length / actionDetailedRecords.value.length) * 100) : 0,
      total_stages_completed: actionDetailedRecords.value.reduce((sum, a) => sum + a.stage_scores.length, 0),
      performance_trends: completedActions.map(a => ({
        action_name: a.action_name,
        score: a.final_score,
        efficiency: a.performance_metrics.completion_efficiency
      }))
    }
    
    // 生成总体评价
    currentSessionReport.value.overall_assessment = generateOverallAssessment()
    
    // 添加元数据
    currentSessionReport.value.metadata = {
      report_generated_time: new Date().toISOString(),
      report_version: '1.0.0',
      total_feedback_records: actionDetailedRecords.value.reduce((sum, a) => sum + a.feedback_timeline.length, 0)
    }
    
    console.log('[TrainingReport] 训练会话报告生成完成')
    return currentSessionReport.value
  }
  
  /**
   * 重置报告数据
   */
  const resetReportData = () => {
    currentSessionReport.value = null
    actionDetailedRecords.value = []
    currentActionRecord.value = null
    sessionStartTime.value = null
  }
  
  // 计算属性
  const isSessionActive = computed(() => {
    return currentSessionReport.value && !currentSessionReport.value.session_info.end_time
  })
  
  const currentSessionStats = computed(() => {
    if (!actionDetailedRecords.value.length) return null
    
    const completed = actionDetailedRecords.value.filter(a => a.status === 'completed')
    return {
      total: actionDetailedRecords.value.length,
      completed: completed.length,
      average_score: completed.length > 0 ? 
        Math.round(completed.reduce((sum, a) => sum + a.final_score, 0) / completed.length) : 0
    }
  })
  
  return {
    // 响应式数据
    currentSessionReport,
    actionDetailedRecords,
    currentActionRecord,
    
    // 计算属性
    isSessionActive,
    currentSessionStats,
    
    // 方法
    startSessionReport,
    startActionRecord,
    recordStageScore,
    recordFeedback,
    completeActionRecord,
    completeSessionReport,
    resetReportData
  }
})
