import { KeyPointMapping } from "@/utils/poseConstants";
import { utils } from "@/utils/geometryUtils";
import { useTrainingReportStore } from '@/stores/trainingReport';

class ShoulderTouchLogic {
    constructor(side = 'left', level = 'medium') {
        this.side = side;
        this.level = level;

        // 初始化报告存储
        this.reportStore = useTrainingReportStore();

        // 动态设置关键点索引
        if (this.side === 'left') {
            this.movingWristIdx = 9; // LEFT_WRIST
            this.movingShoulderIdx = 5; // LEFT_SHOULDER
            this.targetShoulderIdx = 6; // RIGHT_SHOULDER
            this.movingKneeIdx = 13; // LEFT_KNEE (用于判断返回)
            this.movingThumbIdx = 93; // LEFT_THUMB
            this.movingIndexIdx = 97; // LEFT_INDEX
            this.movingPinkyIdx = 109; // LEFT_PINKY
        } else {
            this.movingWristIdx = 10; // RIGHT_WRIST
            this.movingShoulderIdx = 6; // RIGHT_SHOULDER
            this.targetShoulderIdx = 5; // LEFT_SHOULDER
            this.movingKneeIdx = 14; // RIGHT_KNEE (用于判断返回)
            this.movingThumbIdx = 114; // RIGHT_THUMB
            this.movingIndexIdx = 118; // RIGHT_INDEX
            this.movingPinkyIdx = 130; // RIGHT_PINKY
        }

        // 根据难度设置参数 (同前)
        this.config = this.getDifficultyConfig(level);

        // 初始化或重置内部状态
        this.reset();
    }

    getDifficultyConfig(level) {
        switch (level) {
            case 'easy':
                return { holdDuration: 1000, distanceThresholdFactor: 0.30 };
            case 'hard':
                return { holdDuration: 3000, distanceThresholdFactor: 0.10 };
            case 'medium':
            default:
                return { holdDuration: 2000, distanceThresholdFactor: 0.15 };
        }
    }

    reset() {
        this.state = 'IDLE';
        this.score = 0;
        this.feedback = `请准备，将${this.side === 'left' ? '左' : '右'}手掌移动到对侧肩膀。`;

        // 内部状态变量
        this.holdStartTime = 0;
        this.initialDistance = 0;
        this.shoulderWidth = 0;

        // 分阶段得分记录
        this.moveScore = 0      // 移动阶段得分 (0-40)
        this.holdScore = 0      // 保持阶段得分 (0-30)
        this.returnScore = 0    // 返回阶段得分 (0-30)
    }
   
    /**
     * 核心评估方法
     * @param {Array<[number, number, number]>} keypoints - 实时关键点数据
     * @returns {{state: string, score: number, feedback: string}} - 评估结果
     */
    update(keypoints) {
        // 如果动作已完成，则不再更新
        if (this.state === 'COMPLETED') {
            return { state: this.state, score: this.score, feedback: this.feedback };
        }
        // --- 数据提取与前置检查 ---
        const getPoint = (idx) => {
            if (!keypoints[idx] || keypoints[idx].length < 3) {
                return { x: 0, y: 0, c: 0 };
            }
            return { x: keypoints[idx][0], y: keypoints[idx][1], c: keypoints[idx][2] };
        };
        const movingWrist = getPoint(this.movingWristIdx);
        const movingThumb = getPoint(this.movingThumbIdx);
        const movingIndex = getPoint(this.movingIndexIdx);
        const movingPinky = getPoint(this.movingPinkyIdx);
        const movingShoulder = getPoint(this.movingShoulderIdx);
        const targetShoulder = getPoint(this.targetShoulderIdx);
        const movingKnee = getPoint(this.movingKneeIdx);
        if (
          utils.multipleOutOfBounds([movingWrist, movingShoulder, targetShoulder])
        ) {
          return {
            success: false,
            state: this.state,
            score: this.score,
            feedback: "请保证关键身体部位在画面中",
          };
        }
        const handCenter = utils.calculateHandCenter(
          movingWrist,
          movingThumb,
          movingIndex,
          movingPinky
        );
        // --- 几何计算 ---
        const currentDistance = utils.calculateDistance(
          handCenter,
          targetShoulder
        );
        this.shoulderWidth = utils.calculateDistance(movingShoulder, targetShoulder);
        
        const touchThreshold = this.shoulderWidth * this.config.distanceThresholdFactor;
        // --- 状态机与动态计分逻辑 ---
        switch (this.state) {
            case 'IDLE':
                // 等待用户开始动作
                // 条件: 手腕开始向身体中线移动
                const isStarting = (this.side === 'left' && movingWrist.x > movingShoulder.x) || 
                                   (this.side === 'right' && movingWrist.x < movingShoulder.x);
                if (isStarting) {
                    this.state = 'MOVING_TO_TARGET';
                    this.initialDistance = currentDistance; // 捕获起始距离
                    this.feedback = '很好，正在向目标移动...';
                }
                break;

            case 'MOVING_TO_TARGET':
                // 移动阶段评分 (0-40分)
                // 进度 = 1 - (当前距离 / 初始距离)
                const progress = 1 - (currentDistance / this.initialDistance);
                this.moveScore = Math.max(0, Math.min(40, 40 * progress));
                this.score = this.moveScore + this.holdScore + this.returnScore;
                this.feedback = '继续移动...';

                // 条件: 到达目标
                if (currentDistance < touchThreshold) {
                    this.state = 'HOLDING';
                    this.holdStartTime = Date.now();
                    this.moveScore = 40; // 移动阶段满分
                    this.score = this.moveScore + this.holdScore + this.returnScore;
                    this.feedback = `太棒了，请保持 ${this.config.holdDuration / 1000} 秒`;

                    // 记录移动阶段完成
                    this.reportStore.recordStageScore('moving', this.moveScore, 40, '移动到目标阶段完成');
                }
                break;

            case 'HOLDING':
                // 保持阶段评分 (0-30分)
                const holdingTime = Date.now() - this.holdStartTime;

                // 条件1: 手在保持期间移开，则退回上一个状态
                if (currentDistance > touchThreshold * 1.2) { // 使用1.2倍阈值增加容错
                    this.state = 'MOVING_TO_TARGET';
                    this.feedback = '请将手放回肩膀上并保持稳定';
                    // 分数会根据上面的MOVING_TO_TARGET逻辑自动回落，形成惩罚
                    break;
                }

                // 根据保持时间进度计算分数
                const holdProgress = Math.min(1, holdingTime / this.config.holdDuration);
                this.holdScore = Math.max(0, Math.min(30, 30 * holdProgress));
                this.score = this.moveScore + this.holdScore + this.returnScore;

                // 条件2: 保持时间足够
                if (holdProgress >= 1) {
                    this.state = 'RETURNING';
                    this.holdScore = 30; // 保持阶段满分
                    this.score = this.moveScore + this.holdScore + this.returnScore;
                    this.feedback = '很好！现在将手掌返回起始位置';

                    // 记录保持阶段完成
                    this.reportStore.recordStageScore('holding', this.holdScore, 30, '保持触摸阶段完成');
                } else {
                    this.feedback = `保持... 剩余 ${Math.ceil((this.config.holdDuration - holdingTime) / 1000)} 秒`;
                }
                break;

            case 'RETURNING':
                // 返回阶段评分 (0-30分) - 简化的距离判断
                const wristToKneeDistance = utils.calculateDistance(movingWrist, movingKnee);
                const shoulderToKneeDistance = utils.calculateDistance(movingShoulder, movingKnee);

                // 简化的返回进度计算
                const returnProgress = Math.min(1, Math.max(0, (shoulderToKneeDistance - wristToKneeDistance) / shoulderToKneeDistance));
                this.returnScore = Math.max(0, Math.min(30, 30 * returnProgress));
                this.score = this.moveScore + this.holdScore + this.returnScore;
                this.feedback = `正在返回起始位置...`;

                // 简化的完成判定：手腕距离膝盖足够近即可（宽松判定）
                const distanceThreshold = shoulderToKneeDistance * 0.6; // 宽松的60%阈值
                const isHandNearLeg = wristToKneeDistance < distanceThreshold;

                if (isHandNearLeg) {
                    this.state = 'COMPLETED';
                    this.returnScore = Math.max(this.returnScore, 25); // 给予合理的基础分数
                    this.score = this.moveScore + this.holdScore + this.returnScore;
                    this.feedback = `动作完成！最终得分: ${Math.round(this.score)}`;

                    console.log(`[ShoulderTouch] 动作完成 - 手腕到膝盖距离: ${Math.round(wristToKneeDistance)}px, 阈值: ${Math.round(distanceThreshold)}px`);

                    // 记录返回阶段完成
                    this.reportStore.recordStageScore('returning', this.returnScore, 30, '返回起始位置阶段完成');
                }
                break;
        }

        // 记录实时反馈
        this.reportStore.recordFeedback(this.feedback, Math.round(this.score), this.state);

        // 返回实时评估结果，分数取整
        return {
          success: true,
          state: this.state,
          score: Math.round(this.score),
          feedback: this.feedback,
        };
    }
}

export default ShoulderTouchLogic;