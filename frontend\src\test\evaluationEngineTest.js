/**
 * 动作评估引擎测试
 * 用于验证评估引擎的基本功能
 */

import { useActionEvaluationEngine } from '@/composables/useActionEvaluationEngine'

// 模拟关键点数据（133个点，每个点[x, y, confidence]）
function createMockKeypoints() {
  const keypoints = []
  for (let i = 0; i < 133; i++) {
    keypoints.push([Math.random(), Math.random(), 0.8])
  }
  
  // 设置一些关键的身体部位点
  keypoints[5] = [0.4, 0.3, 0.9]  // LEFT_SHOULDER
  keypoints[6] = [0.6, 0.3, 0.9]  // RIGHT_SHOULDER
  keypoints[7] = [0.35, 0.4, 0.9] // LEFT_ELBOW
  keypoints[8] = [0.65, 0.4, 0.9] // RIGHT_ELBOW
  keypoints[9] = [0.3, 0.5, 0.9]  // LEFT_WRIST
  keypoints[10] = [0.7, 0.5, 0.9] // RIGHT_WRIST
  
  return keypoints
}

// 测试函数
export function testEvaluationEngine() {
  console.log('=== 动作评估引擎测试开始 ===')
  
  const engine = useActionEvaluationEngine()
  
  // 测试1: 加载肩膀触摸检测器
  console.log('\n测试1: 加载肩膀触摸检测器')
  const result1 = engine.loadDetector('shoulder_touch', 'left', 'medium')
  console.log('加载结果:', result1)
  console.log('当前动作类型:', engine.currentActionType.value)
  console.log('是否激活:', engine.isActive.value)
  
  // 测试2: 更新评估
  console.log('\n测试2: 更新评估')
  const mockKeypoints = createMockKeypoints()
  const evalResult = engine.updateEvaluation(mockKeypoints)
  console.log('评估结果:', evalResult)
  console.log('当前分数:', engine.currentScore.value)
  console.log('当前反馈:', engine.currentFeedback.value)
  
  // 测试3: 加载手臂上举检测器
  console.log('\n测试3: 加载手臂上举检测器')
  const result3 = engine.loadDetector('arm_raise', 'right', 'easy')
  console.log('加载结果:', result3)
  console.log('当前动作类型:', engine.currentActionType.value)
  
  // 测试4: 重置检测器
  console.log('\n测试4: 重置检测器')
  engine.resetDetector()
  console.log('重置后分数:', engine.currentScore.value)
  console.log('重置后反馈:', engine.currentFeedback.value)
  
  // 测试5: 停止评估
  console.log('\n测试5: 停止评估')
  engine.stopEvaluation()
  console.log('停止后是否激活:', engine.isActive.value)
  console.log('停止后动作类型:', engine.currentActionType.value)
  
  console.log('\n=== 动作评估引擎测试完成 ===')
  
  return {
    loadDetector: result1,
    evaluation: evalResult,
    armRaiseLoad: result3
  }
}

// 在浏览器控制台中运行测试
if (typeof window !== 'undefined') {
  window.testEvaluationEngine = testEvaluationEngine
  console.log('测试函数已添加到 window.testEvaluationEngine，可在控制台中调用')
}

export default testEvaluationEngine
