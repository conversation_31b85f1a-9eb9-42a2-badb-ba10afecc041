/**
 * 测试精简的无接触异常处理系统
 */

import { useSimpleIdentityVerification } from '@/composables/useSimpleIdentityVerification'
import { useTouchlessActionTimeout } from '@/composables/useTouchlessActionTimeout'

// 模拟测试函数
export function testTouchlessSystem() {
  console.log('🧪 开始测试无接触异常处理系统')
  
  // 测试身份验证
  console.log('📋 测试1: 身份验证系统')
  const simpleIdentity = useSimpleIdentityVerification()
  
  console.log('✅ 身份验证composable创建成功')
  console.log('   - isVerificationActive:', simpleIdentity.isVerificationActive.value)
  console.log('   - currentIssueType:', simpleIdentity.currentIssueType.value)
  console.log('   - DETECTION_TIMEOUT:', simpleIdentity.DETECTION_TIMEOUT)
  console.log('   - TRAINING_RESET_TIMEOUT:', simpleIdentity.TRAINING_RESET_TIMEOUT)
  console.log('   - REPORT_RESET_TIMEOUT:', simpleIdentity.REPORT_RESET_TIMEOUT)
  console.log('   - OTHER_RESET_TIMEOUT:', simpleIdentity.OTHER_RESET_TIMEOUT)
  
  // 测试超时检测
  console.log('📋 测试2: 超时检测系统')
  const touchlessTimeout = useTouchlessActionTimeout()
  
  console.log('✅ 超时检测composable创建成功')
  console.log('   - isDetectionActive:', touchlessTimeout.isDetectionActive.value)
  console.log('   - elapsedTime:', touchlessTimeout.elapsedTime.value)
  console.log('   - TIMEOUT_CONFIG:', touchlessTimeout.TIMEOUT_CONFIG)
  
  // 模拟启动身份验证
  console.log('📋 测试3: 启动身份验证')
  const mockCallbacks = {
    onUserMissing: (data) => console.log('🚨 用户消失回调:', data),
    onUnauthorizedUser: (data) => console.log('🚨 未授权用户回调:', data),
    onUserReturned: (data) => console.log('✅ 用户返回回调:', data),
    onResetToLogin: (data) => console.log('🔄 重置到登录回调:', data)
  }
  
  // 注意：这里需要模拟patientStore有用户信息
  // const result = simpleIdentity.startVerification(mockCallbacks)
  // console.log('身份验证启动结果:', result)
  
  // 模拟启动超时检测
  console.log('📋 测试4: 启动超时检测')
  const mockAction = {
    action_id: 'test_action',
    action_name: '测试动作',
    action_type: 'arm_raise'
  }
  
  const mockTimeoutCallbacks = {
    onTimeout: (data) => console.log('⏰ 超时回调:', data),
    onWarning: (data) => console.log('⚠️ 警告回调:', data),
    onStagnation: (data) => console.log('🐌 停滞回调:', data)
  }
  
  touchlessTimeout.startDetection(mockAction, mockTimeoutCallbacks)
  console.log('✅ 超时检测启动成功')
  console.log('   - isDetectionActive:', touchlessTimeout.isDetectionActive.value)
  
  // 模拟进度更新
  console.log('📋 测试5: 进度更新')
  touchlessTimeout.updateProgress('in_progress', 0.5)
  console.log('✅ 进度更新完成')
  
  // 清理
  setTimeout(() => {
    console.log('🧹 清理测试资源')
    touchlessTimeout.stopDetection()
    simpleIdentity.stopVerification()
    console.log('✅ 测试完成')
  }, 1000)
  
  return {
    simpleIdentity,
    touchlessTimeout,
    success: true
  }
}

// 在控制台中运行测试
if (typeof window !== 'undefined') {
  window.testTouchlessSystem = testTouchlessSystem
  console.log('💡 在控制台中运行 testTouchlessSystem() 来测试系统')

  // 修复总结
  console.log('🔧 身份验证系统优化总结:')
  console.log('   1. ✅ 修复了 recovery-delay 从 10秒 改为 60秒')
  console.log('   2. ✅ 禁用了身份验证异常的自动恢复')
  console.log('   3. ✅ 禁用了身份验证异常的手动恢复')
  console.log('   4. ✅ 只允许用户返回或超时重置两种方式')
  console.log('   5. ✅ 修复了 addActionRecord 函数调用错误')
  console.log('   6. ✅ 添加了身份验证异常的倒计时显示')
  console.log('   7. ✅ 修复了 shoulderTouchLogic.js 中的 keypoints 访问错误')
  console.log('   8. ✅ 修复了启动时立即检测问题，增加5秒缓冲时间')
  console.log('   9. ✅ 修复了 useTouchlessActionTimeout.js 中的 exceptionStore 错误')
  console.log('   10. ✅ 添加了防抖机制，解决时序问题导致的误判')
  console.log('   11. ✅ 彻底重构优化代码，从500+行精简到300+行')
  console.log('   12. ✅ 实现动态配置倒计时显示，根据当前阶段自动调整')
  console.log('')
  console.log('📋 优化后的行为:')
  console.log('   - 启动身份验证后5秒 → 开始首次检测')
  console.log('   - 用户消失3秒 → 暂停训练，显示动态倒计时')
  console.log('   - 用户返回 → 立即恢复训练（最高优先级，无防抖延迟）')
  console.log('   - 训练阶段超时(10秒) → 保存记录并跳转报告页面')
  console.log('   - 报告阶段超时(30秒) → 自动重置系统返回登录页')
  console.log('   - 其他阶段超时(60秒) → 直接重置到登录页')
  console.log('   - 动作超时 → 20秒警告，90秒自动跳过（已调整为测试值）')
  console.log('')
  console.log('🎯 代码优化成果:')
  console.log('   - 统一的问题处理逻辑 (handleIssue)')
  console.log('   - 统一的触发动作逻辑 (triggerIssueAction)')
  console.log('   - 动态配置支持 (currentTimeoutConfig)')
  console.log('   - 简化的定时器管理 (clearAllTimers)')
  console.log('   - 精简的防抖机制')
}
