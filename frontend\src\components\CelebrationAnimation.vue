<template>
  <teleport to="body">
    <transition name="celebration" appear>
      <div
        v-if="isVisible"
        class="celebration-overlay"
        @click="handleSkip"
      >
        <!-- 背景渐变 -->
        <div class="celebration-background"></div>
        
        <!-- 烟花粒子容器 -->
        <div class="fireworks-container">
          <div
            v-for="(firework, index) in fireworks"
            :key="`firework-${index}`"
            class="firework"
            :style="firework.style"
          ></div>
        </div>
        
        <!-- 彩带飘落 -->
        <div class="confetti-container">
          <div
            v-for="(confetti, index) in confettiPieces"
            :key="`confetti-${index}`"
            class="confetti"
            :style="confetti.style"
          ></div>
        </div>
        
        <!-- 主要内容 -->
        <div class="celebration-content">
          <!-- 成功图标 -->
          <div class="success-icon-container">
            <div class="success-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
          </div>
          
          <!-- 恭喜文字 -->
          <div class="congratulations-text">
            <h1 class="main-title">恭喜完成！</h1>
            <h2 class="action-name">{{ actionName }}</h2>
          </div>
          
          <!-- 得分显示 -->
          <div class="score-display">
            <div class="score-label">得分</div>
            <div class="score-value">{{ animatedScore }}</div>
            <div class="score-unit">分</div>
          </div>
          
          <!-- 进度指示 -->
          <div class="progress-indicator">
            <div class="progress-text">
              {{ currentActionIndex + 1 }} / {{ totalActions }}
            </div>
            <div class="progress-bar">
              <div 
                class="progress-fill"
                :style="{ width: `${progressPercentage}%` }"
              ></div>
            </div>
          </div>
          
          <!-- 下一步提示 -->
          <div class="next-action-hint" v-if="hasNextAction">
            <div class="hint-text">下一个动作</div>
            <div class="next-action-name">{{ nextActionName }}</div>
          </div>
          
          <!-- 完成提示 -->
          <div class="completion-hint" v-else>
            <div class="hint-text">🎉 所有动作已完成！</div>
          </div>
        </div>
        
        <!-- 跳过按钮 -->
        <button class="skip-button" @click="handleSkip">
          跳过动画
        </button>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

// Props
const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false
  },
  actionName: {
    type: String,
    default: '动作训练'
  },
  score: {
    type: Number,
    default: 0
  },
  currentActionIndex: {
    type: Number,
    default: 0
  },
  totalActions: {
    type: Number,
    default: 1
  },
  nextActionName: {
    type: String,
    default: ''
  },
  duration: {
    type: Number,
    default: 3000 // 默认3秒
  }
})

// Emits
const emit = defineEmits(['complete', 'skip'])

// 响应式数据
const animatedScore = ref(0)
const fireworks = ref([])
const confettiPieces = ref([])
const animationTimer = ref(null)

// 计算属性
const hasNextAction = computed(() => {
  return props.currentActionIndex < props.totalActions - 1
})

const progressPercentage = computed(() => {
  return ((props.currentActionIndex + 1) / props.totalActions) * 100
})

// 动画控制方法
const startCelebration = () => {
  console.log('[CelebrationAnimation] 开始庆祝动画')
  
  // 重置动画状态
  animatedScore.value = 0
  fireworks.value = []
  confettiPieces.value = []
  
  // 启动各种动画
  animateScore()
  createFireworks()
  createConfetti()
  
  // 设置自动完成定时器
  if (animationTimer.value) {
    clearTimeout(animationTimer.value)
  }
  
  animationTimer.value = setTimeout(() => {
    handleComplete()
  }, props.duration)
}

// 得分动画
const animateScore = () => {
  const duration = 1000 // 1秒内完成得分动画
  const startTime = Date.now()
  const startScore = 0
  const targetScore = props.score
  
  const updateScore = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.min(elapsed / duration, 1)
    
    // 使用缓动函数
    const easeOutQuart = 1 - Math.pow(1 - progress, 4)
    animatedScore.value = Math.round(startScore + (targetScore - startScore) * easeOutQuart)
    
    if (progress < 1) {
      requestAnimationFrame(updateScore)
    }
  }
  
  requestAnimationFrame(updateScore)
}

// 创建烟花效果
const createFireworks = () => {
  const fireworkCount = 8
  
  for (let i = 0; i < fireworkCount; i++) {
    setTimeout(() => {
      const firework = {
        style: {
          left: `${Math.random() * 80 + 10}%`,
          top: `${Math.random() * 60 + 20}%`,
          animationDelay: `${Math.random() * 0.5}s`,
          animationDuration: `${1.5 + Math.random() * 0.5}s`
        }
      }
      fireworks.value.push(firework)
    }, i * 200)
  }
}

// 创建彩带效果
const createConfetti = () => {
  const confettiCount = 50
  const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff']
  
  for (let i = 0; i < confettiCount; i++) {
    setTimeout(() => {
      const confetti = {
        style: {
          left: `${Math.random() * 100}%`,
          backgroundColor: colors[Math.floor(Math.random() * colors.length)],
          animationDelay: `${Math.random() * 2}s`,
          animationDuration: `${3 + Math.random() * 2}s`,
          transform: `rotate(${Math.random() * 360}deg)`
        }
      }
      confettiPieces.value.push(confetti)
    }, i * 50)
  }
}

// 事件处理
const handleComplete = () => {
  console.log('[CelebrationAnimation] 庆祝动画完成')
  emit('complete')
}

const handleSkip = () => {
  console.log('[CelebrationAnimation] 跳过庆祝动画')
  if (animationTimer.value) {
    clearTimeout(animationTimer.value)
    animationTimer.value = null
  }
  emit('skip')
}

// 监听可见性变化
watch(() => props.isVisible, (newVisible) => {
  if (newVisible) {
    startCelebration()
  } else {
    // 清理定时器
    if (animationTimer.value) {
      clearTimeout(animationTimer.value)
      animationTimer.value = null
    }
  }
})

// 生命周期
onUnmounted(() => {
  if (animationTimer.value) {
    clearTimeout(animationTimer.value)
  }
})
</script>

<style scoped>
/* 庆祝动画覆盖层 */
.celebration-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  cursor: pointer;
}

/* 背景渐变 */
.celebration-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(255, 107, 107, 0.9) 0%,
    rgba(78, 205, 196, 0.9) 25%,
    rgba(69, 183, 209, 0.9) 50%,
    rgba(150, 206, 180, 0.9) 75%,
    rgba(254, 202, 87, 0.9) 100%
  );
  animation: backgroundPulse 3s ease-in-out infinite;
}

@keyframes backgroundPulse {
  0%, 100% { opacity: 0.9; }
  50% { opacity: 0.7; }
}

/* 烟花容器 */
.fireworks-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* 烟花效果 */
.firework {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: radial-gradient(circle, #fff 0%, #ff6b6b 50%, transparent 70%);
  animation: fireworkExplode 2s ease-out forwards;
}

@keyframes fireworkExplode {
  0% {
    transform: scale(0);
    opacity: 1;
    box-shadow:
      0 0 0 0 rgba(255, 255, 255, 0.8),
      0 0 0 0 rgba(255, 107, 107, 0.6);
  }
  50% {
    transform: scale(1);
    opacity: 1;
    box-shadow:
      0 0 20px 10px rgba(255, 255, 255, 0.4),
      0 0 40px 20px rgba(255, 107, 107, 0.3);
  }
  100% {
    transform: scale(3);
    opacity: 0;
    box-shadow:
      0 0 50px 25px rgba(255, 255, 255, 0.1),
      0 0 80px 40px rgba(255, 107, 107, 0.1);
  }
}

/* 彩带容器 */
.confetti-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* 彩带效果 */
.confetti {
  position: absolute;
  top: -10px;
  width: 8px;
  height: 8px;
  border-radius: 2px;
  animation: confettiFall 5s linear forwards;
}

@keyframes confettiFall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(720deg);
    opacity: 0;
  }
}

/* 主要内容 */
.celebration-content {
  position: relative;
  z-index: 10;
  text-align: center;
  color: white;
  max-width: 500px;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: contentSlideIn 0.8s ease-out forwards;
}

@keyframes contentSlideIn {
  0% {
    transform: translateY(50px) scale(0.9);
    opacity: 0;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* 成功图标 */
.success-icon-container {
  margin-bottom: 1.5rem;
  animation: iconBounce 1s ease-out 0.3s forwards;
  transform: scale(0);
}

.success-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 30px rgba(78, 205, 196, 0.4);
}

.success-icon svg {
  width: 40px;
  height: 40px;
  color: white;
}

@keyframes iconBounce {
  0% { transform: scale(0); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* 恭喜文字 */
.congratulations-text {
  margin-bottom: 2rem;
}

.main-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  background: linear-gradient(45deg, #fff, #feca57, #ff9ff3);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: titleGlow 2s ease-in-out infinite;
}

.action-name {
  font-size: 1.5rem;
  font-weight: 600;
  opacity: 0.9;
  animation: fadeInUp 0.8s ease-out 0.5s forwards;
  transform: translateY(20px);
  opacity: 0;
}

@keyframes titleGlow {
  0%, 100% { filter: brightness(1); }
  50% { filter: brightness(1.2); }
}

@keyframes fadeInUp {
  to {
    transform: translateY(0);
    opacity: 0.9;
  }
}

/* 得分显示 */
.score-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  animation: scoreSlideIn 1s ease-out 0.7s forwards;
  transform: translateX(-50px);
  opacity: 0;
}

.score-label {
  font-size: 1.2rem;
  font-weight: 500;
  opacity: 0.8;
}

.score-value {
  font-size: 3rem;
  font-weight: bold;
  background: linear-gradient(45deg, #feca57, #ff9ff3, #54a0ff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.score-unit {
  font-size: 1.2rem;
  font-weight: 500;
  opacity: 0.8;
}

@keyframes scoreSlideIn {
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 进度指示 */
.progress-indicator {
  margin-bottom: 2rem;
  animation: progressFadeIn 0.8s ease-out 1s forwards;
  opacity: 0;
}

.progress-text {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  opacity: 0.8;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ecdc4, #44a08d);
  border-radius: 4px;
  transition: width 1s ease-out;
  animation: progressGlow 2s ease-in-out infinite;
}

@keyframes progressFadeIn {
  to { opacity: 1; }
}

@keyframes progressGlow {
  0%, 100% { box-shadow: 0 0 5px rgba(78, 205, 196, 0.5); }
  50% { box-shadow: 0 0 15px rgba(78, 205, 196, 0.8); }
}

/* 下一步提示 */
.next-action-hint, .completion-hint {
  animation: hintSlideUp 0.8s ease-out 1.2s forwards;
  transform: translateY(30px);
  opacity: 0;
}

.hint-text {
  font-size: 1rem;
  opacity: 0.7;
  margin-bottom: 0.5rem;
}

.next-action-name {
  font-size: 1.3rem;
  font-weight: 600;
  color: #feca57;
}

@keyframes hintSlideUp {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 跳过按钮 */
.skip-button {
  position: absolute;
  top: 2rem;
  right: 2rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.skip-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

/* 庆祝动画过渡 */
.celebration-enter-active {
  transition: all 0.5s ease-out;
}

.celebration-leave-active {
  transition: all 0.3s ease-in;
}

.celebration-enter-from {
  opacity: 0;
  transform: scale(0.9);
}

.celebration-leave-to {
  opacity: 0;
  transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .celebration-content {
    max-width: 90%;
    padding: 1.5rem;
  }

  .main-title {
    font-size: 2rem;
  }

  .score-value {
    font-size: 2.5rem;
  }

  .success-icon {
    width: 60px;
    height: 60px;
  }

  .success-icon svg {
    width: 30px;
    height: 30px;
  }
}
</style>
