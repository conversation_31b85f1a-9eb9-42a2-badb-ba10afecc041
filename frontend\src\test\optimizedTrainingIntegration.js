/**
 * 优化后的训练集成测试
 * 验证 TrainingView 与 ShoulderTouchLogic 的优化集成
 */

import { ref } from 'vue'
import ShoulderTouchLogic from '@/composables/gemini/ShoulderTouchLogic'

// 模拟 useSimplifiedTrainingSession 的返回值
function createMockTrainingSession() {
  const shoulderTouchLogic = new ShoulderTouchLogic('left', 'medium')
  const isTrainingActive = ref(true)
  const currentProgress = ref(0)
  const currentFeedback = ref('')
  
  const getDetectorState = () => ({
    state: shoulderTouchLogic.state,
    score: shoulderTouchLogic.score,
    feedback: shoulderTouchLogic.feedback
  })
  
  // 模拟更新方法
  const updateWithKeypoints = (keypoints) => {
    const result = shoulderTouchLogic.update(keypoints)
    currentProgress.value = result.score
    currentFeedback.value = result.feedback
    return result
  }
  
  return {
    isTrainingActive,
    currentProgress,
    currentFeedback,
    getDetectorState,
    updateWithKeypoints,
    shoulderTouchLogic: () => shoulderTouchLogic
  }
}

// 模拟 TrainingView 的计算属性
function createMockTrainingView(trainingSession) {
  // 直接使用 ShoulderTouchLogic 的反馈
  const currentFeedback = () => ({
    text: trainingSession.currentFeedback.value || '准备开始训练',
    type: 'info'
  })
  
  // 简化指导文本
  const currentGuidance = () => trainingSession.currentFeedback.value || ''
  
  // 基于 ShoulderTouchLogic 状态的动作阶段
  const currentActionStage = () => {
    const detectorState = trainingSession.getDetectorState()
    const state = detectorState.state || 'IDLE'
    
    switch (state) {
      case 'COMPLETED': return 'completed'
      case 'RETURNING': return 'peak'
      case 'HOLDING': return 'peak'
      case 'MOVING_TO_TARGET': return 'approaching'
      case 'IDLE':
      default: return 'waiting'
    }
  }
  
  // 简化指导显示逻辑
  const shouldShowGuidance = () => {
    const feedback = trainingSession.currentFeedback.value
    return feedback && 
           feedback !== '' && 
           feedback !== '准备开始训练' &&
           feedback !== '请保证关键身体部位在画面中'
  }
  
  return {
    currentFeedback,
    currentGuidance,
    currentActionStage,
    shouldShowGuidance
  }
}

// 创建测试关键点序列
function createTestKeypointsSequence() {
  const sequences = []
  
  // 1. IDLE 状态 - 手在身体两侧
  const idleKeypoints = new Array(133).fill(null).map(() => [0.5, 0.5, 0.9])
  idleKeypoints[11] = [0.3, 0.4, 0.9]  // 左肩膀
  idleKeypoints[12] = [0.7, 0.4, 0.9]  // 右肩膀
  idleKeypoints[16] = [0.8, 0.6, 0.9]  // 右手腕在身体右侧
  sequences.push({ keypoints: idleKeypoints, expectedState: 'IDLE', description: '初始状态' })
  
  // 2. MOVING_TO_TARGET 状态 - 手开始移动
  const movingKeypoints = [...idleKeypoints]
  movingKeypoints[16] = [0.6, 0.5, 0.9]  // 右手腕向左移动
  sequences.push({ keypoints: movingKeypoints, expectedState: 'MOVING_TO_TARGET', description: '开始移动' })
  
  // 3. HOLDING 状态 - 手触摸肩膀
  const holdingKeypoints = [...idleKeypoints]
  holdingKeypoints[16] = [0.3, 0.4, 0.9]  // 右手腕触摸左肩膀
  sequences.push({ keypoints: holdingKeypoints, expectedState: 'HOLDING', description: '触摸肩膀' })
  
  // 4. RETURNING 状态 - 手返回
  const returningKeypoints = [...idleKeypoints]
  returningKeypoints[16] = [0.6, 0.5, 0.9]  // 右手腕返回中
  sequences.push({ keypoints: returningKeypoints, expectedState: 'RETURNING', description: '返回中' })
  
  // 5. COMPLETED 状态 - 手回到原位
  const completedKeypoints = [...idleKeypoints]
  completedKeypoints[16] = [0.8, 0.6, 0.9]  // 右手腕回到原位
  sequences.push({ keypoints: completedKeypoints, expectedState: 'COMPLETED', description: '动作完成' })
  
  return sequences
}

// 测试优化后的集成
function testOptimizedIntegration() {
  console.log('=== 优化后的训练集成测试开始 ===')
  
  // 1. 创建模拟的训练会话
  const trainingSession = createMockTrainingSession()
  const trainingView = createMockTrainingView(trainingSession)
  
  console.log('\n1. 初始状态测试:')
  console.log('训练激活:', trainingSession.isTrainingActive.value)
  console.log('当前进度:', trainingSession.currentProgress.value)
  console.log('当前反馈:', trainingSession.currentFeedback.value)
  console.log('动作阶段:', trainingView.currentActionStage())
  console.log('显示指导:', trainingView.shouldShowGuidance())
  
  // 2. 测试状态转换
  console.log('\n2. 状态转换测试:')
  const sequences = createTestKeypointsSequence()
  
  sequences.forEach((sequence, index) => {
    console.log(`\n--- 步骤 ${index + 1}: ${sequence.description} ---`)
    
    // 更新关键点
    const result = trainingSession.updateWithKeypoints(sequence.keypoints)
    
    // 检查结果
    console.log('ShoulderTouchLogic 状态:', result.state)
    console.log('期望状态:', sequence.expectedState)
    console.log('分数:', result.score)
    console.log('反馈:', result.feedback)
    console.log('UI 阶段:', trainingView.currentActionStage())
    console.log('显示指导:', trainingView.shouldShowGuidance())
    console.log('指导文本:', trainingView.currentGuidance())
    
    // 验证状态匹配
    if (result.state === sequence.expectedState) {
      console.log('✅ 状态匹配正确')
    } else {
      console.log('❌ 状态不匹配')
    }
  })
  
  console.log('\n=== 优化后的训练集成测试完成 ===')
  
  return {
    trainingSession,
    trainingView,
    sequences
  }
}

// 导出测试函数
export { 
  testOptimizedIntegration, 
  createMockTrainingSession, 
  createMockTrainingView,
  createTestKeypointsSequence 
}

// 浏览器环境支持
if (typeof window !== 'undefined') {
  window.testOptimizedIntegration = testOptimizedIntegration
  console.log('优化集成测试函数已添加到 window.testOptimizedIntegration')
}
