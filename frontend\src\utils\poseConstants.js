/**
 * COCO-WholeBody (133个关键点) 的骨架连接定义
 * 基于提供的skeleton_info重新定义，包含65个连接和颜色信息
 *
 * 每个连接包含：
 * - connection: [id1, id2] 关键点连接
 * - color: [r, g, b] RGB颜色值
 * - id: 连接的唯一标识符
 */

// 骨架连接数组 - 仅包含连接点对，用于向后兼容
export const COCO_WHOLEBODY_CONNECTIONS = [
  // 0-4: 腿部连接
  [15, 13], // left_ankle -> left_knee
  [13, 11], // left_knee -> left_hip
  [16, 14], // right_ankle -> right_knee
  [14, 12], // right_knee -> right_hip

  // 4-7: 躯干连接
  [11, 12], // left_hip -> right_hip
  [5, 11],  // left_shoulder -> left_hip
  [6, 12],  // right_shoulder -> right_hip
  [5, 6],   // left_shoulder -> right_shoulder

  // 8-11: 手臂连接
  [5, 7],   // left_shoulder -> left_elbow
  [6, 8],   // right_shoulder -> right_elbow
  [7, 9],   // left_elbow -> left_wrist
  [8, 10],  // right_elbow -> right_wrist

  // 12-18: 头部连接
  [1, 2],   // left_eye -> right_eye
  [0, 1],   // nose -> left_eye
  [0, 2],   // nose -> right_eye
  [1, 3],   // left_eye -> left_ear
  [2, 4],   // right_eye -> right_ear
  [3, 5],   // left_ear -> left_shoulder
  [4, 6],   // right_ear -> right_shoulder

  // 19-24: 脚部连接
  [15, 17], // left_ankle -> left_big_toe
  [15, 18], // left_ankle -> left_small_toe
  [15, 19], // left_ankle -> left_heel
  [16, 20], // right_ankle -> right_big_toe
  [16, 21], // right_ankle -> right_small_toe
  [16, 22], // right_ankle -> right_heel

  // 25-44: 左手连接
  [91, 92], // left_hand_root -> left_thumb1
  [92, 93], // left_thumb1 -> left_thumb2
  [93, 94], // left_thumb2 -> left_thumb3
  [94, 95], // left_thumb3 -> left_thumb4
  [91, 96], // left_hand_root -> left_forefinger1
  [96, 97], // left_forefinger1 -> left_forefinger2
  [97, 98], // left_forefinger2 -> left_forefinger3
  [98, 99], // left_forefinger3 -> left_forefinger4
  [91, 100], // left_hand_root -> left_middle_finger1
  [100, 101], // left_middle_finger1 -> left_middle_finger2
  [101, 102], // left_middle_finger2 -> left_middle_finger3
  [102, 103], // left_middle_finger3 -> left_middle_finger4
  [91, 104], // left_hand_root -> left_ring_finger1
  [104, 105], // left_ring_finger1 -> left_ring_finger2
  [105, 106], // left_ring_finger2 -> left_ring_finger3
  [106, 107], // left_ring_finger3 -> left_ring_finger4
  [91, 108], // left_hand_root -> left_pinky_finger1
  [108, 109], // left_pinky_finger1 -> left_pinky_finger2
  [109, 110], // left_pinky_finger2 -> left_pinky_finger3
  [110, 111], // left_pinky_finger3 -> left_pinky_finger4

  // 45-64: 右手连接
  [112, 113], // right_hand_root -> right_thumb1
  [113, 114], // right_thumb1 -> right_thumb2
  [114, 115], // right_thumb2 -> right_thumb3
  [115, 116], // right_thumb3 -> right_thumb4
  [112, 117], // right_hand_root -> right_forefinger1
  [117, 118], // right_forefinger1 -> right_forefinger2
  [118, 119], // right_forefinger2 -> right_forefinger3
  [119, 120], // right_forefinger3 -> right_forefinger4
  [112, 121], // right_hand_root -> right_middle_finger1
  [121, 122], // right_middle_finger1 -> right_middle_finger2
  [122, 123], // right_middle_finger2 -> right_middle_finger3
  [123, 124], // right_middle_finger3 -> right_middle_finger4
  [112, 125], // right_hand_root -> right_ring_finger1
  [125, 126], // right_ring_finger1 -> right_ring_finger2
  [126, 127], // right_ring_finger2 -> right_ring_finger3
  [127, 128], // right_ring_finger3 -> right_ring_finger4
  [112, 129], // right_hand_root -> right_pinky_finger1
  [129, 130], // right_pinky_finger1 -> right_pinky_finger2
  [130, 131], // right_pinky_finger2 -> right_pinky_finger3
  [131, 132], // right_pinky_finger3 -> right_pinky_finger4
];

// 详细的骨架连接信息，包含颜色和ID
export const SKELETON_INFO = {
  0: { link: ['left_ankle', 'left_knee'], id: 0, color: [0, 255, 0] },
  1: { link: ['left_knee', 'left_hip'], id: 1, color: [0, 255, 0] },
  2: { link: ['right_ankle', 'right_knee'], id: 2, color: [255, 128, 0] },
  3: { link: ['right_knee', 'right_hip'], id: 3, color: [255, 128, 0] },
  4: { link: ['left_hip', 'right_hip'], id: 4, color: [51, 153, 255] },
  5: { link: ['left_shoulder', 'left_hip'], id: 5, color: [51, 153, 255] },
  6: { link: ['right_shoulder', 'right_hip'], id: 6, color: [51, 153, 255] },
  7: { link: ['left_shoulder', 'right_shoulder'], id: 7, color: [51, 153, 255] },
  8: { link: ['left_shoulder', 'left_elbow'], id: 8, color: [0, 255, 0] },
  9: { link: ['right_shoulder', 'right_elbow'], id: 9, color: [255, 128, 0] },
  10: { link: ['left_elbow', 'left_wrist'], id: 10, color: [0, 255, 0] },
  11: { link: ['right_elbow', 'right_wrist'], id: 11, color: [255, 128, 0] },
  12: { link: ['left_eye', 'right_eye'], id: 12, color: [51, 153, 255] },
  13: { link: ['nose', 'left_eye'], id: 13, color: [51, 153, 255] },
  14: { link: ['nose', 'right_eye'], id: 14, color: [51, 153, 255] },
  15: { link: ['left_eye', 'left_ear'], id: 15, color: [51, 153, 255] },
  16: { link: ['right_eye', 'right_ear'], id: 16, color: [51, 153, 255] },
  17: { link: ['left_ear', 'left_shoulder'], id: 17, color: [51, 153, 255] },
  18: { link: ['right_ear', 'right_shoulder'], id: 18, color: [51, 153, 255] },
  19: { link: ['left_ankle', 'left_big_toe'], id: 19, color: [0, 255, 0] },
  20: { link: ['left_ankle', 'left_small_toe'], id: 20, color: [0, 255, 0] },
  21: { link: ['left_ankle', 'left_heel'], id: 21, color: [0, 255, 0] },
  22: { link: ['right_ankle', 'right_big_toe'], id: 22, color: [255, 128, 0] },
  23: { link: ['right_ankle', 'right_small_toe'], id: 23, color: [255, 128, 0] },
  24: { link: ['right_ankle', 'right_heel'], id: 24, color: [255, 128, 0] },
  25: { link: ['left_hand_root', 'left_thumb1'], id: 25, color: [255, 128, 0] },
  26: { link: ['left_thumb1', 'left_thumb2'], id: 26, color: [255, 128, 0] },
  27: { link: ['left_thumb2', 'left_thumb3'], id: 27, color: [255, 128, 0] },
  28: { link: ['left_thumb3', 'left_thumb4'], id: 28, color: [255, 128, 0] },
  29: { link: ['left_hand_root', 'left_forefinger1'], id: 29, color: [255, 153, 255] },
  30: { link: ['left_forefinger1', 'left_forefinger2'], id: 30, color: [255, 153, 255] },
  31: { link: ['left_forefinger2', 'left_forefinger3'], id: 31, color: [255, 153, 255] },
  32: { link: ['left_forefinger3', 'left_forefinger4'], id: 32, color: [255, 153, 255] },
  33: { link: ['left_hand_root', 'left_middle_finger1'], id: 33, color: [102, 178, 255] },
  34: { link: ['left_middle_finger1', 'left_middle_finger2'], id: 34, color: [102, 178, 255] },
  35: { link: ['left_middle_finger2', 'left_middle_finger3'], id: 35, color: [102, 178, 255] },
  36: { link: ['left_middle_finger3', 'left_middle_finger4'], id: 36, color: [102, 178, 255] },
  37: { link: ['left_hand_root', 'left_ring_finger1'], id: 37, color: [255, 51, 51] },
  38: { link: ['left_ring_finger1', 'left_ring_finger2'], id: 38, color: [255, 51, 51] },
  39: { link: ['left_ring_finger2', 'left_ring_finger3'], id: 39, color: [255, 51, 51] },
  40: { link: ['left_ring_finger3', 'left_ring_finger4'], id: 40, color: [255, 51, 51] },
  41: { link: ['left_hand_root', 'left_pinky_finger1'], id: 41, color: [0, 255, 0] },
  42: { link: ['left_pinky_finger1', 'left_pinky_finger2'], id: 42, color: [0, 255, 0] },
  43: { link: ['left_pinky_finger2', 'left_pinky_finger3'], id: 43, color: [0, 255, 0] },
  44: { link: ['left_pinky_finger3', 'left_pinky_finger4'], id: 44, color: [0, 255, 0] },
  45: { link: ['right_hand_root', 'right_thumb1'], id: 45, color: [255, 128, 0] },
  46: { link: ['right_thumb1', 'right_thumb2'], id: 46, color: [255, 128, 0] },
  47: { link: ['right_thumb2', 'right_thumb3'], id: 47, color: [255, 128, 0] },
  48: { link: ['right_thumb3', 'right_thumb4'], id: 48, color: [255, 128, 0] },
  49: { link: ['right_hand_root', 'right_forefinger1'], id: 49, color: [255, 153, 255] },
  50: { link: ['right_forefinger1', 'right_forefinger2'], id: 50, color: [255, 153, 255] },
  51: { link: ['right_forefinger2', 'right_forefinger3'], id: 51, color: [255, 153, 255] },
  52: { link: ['right_forefinger3', 'right_forefinger4'], id: 52, color: [255, 153, 255] },
  53: { link: ['right_hand_root', 'right_middle_finger1'], id: 53, color: [102, 178, 255] },
  54: { link: ['right_middle_finger1', 'right_middle_finger2'], id: 54, color: [102, 178, 255] },
  55: { link: ['right_middle_finger2', 'right_middle_finger3'], id: 55, color: [102, 178, 255] },
  56: { link: ['right_middle_finger3', 'right_middle_finger4'], id: 56, color: [102, 178, 255] },
  57: { link: ['right_hand_root', 'right_ring_finger1'], id: 57, color: [255, 51, 51] },
  58: { link: ['right_ring_finger1', 'right_ring_finger2'], id: 58, color: [255, 51, 51] },
  59: { link: ['right_ring_finger2', 'right_ring_finger3'], id: 59, color: [255, 51, 51] },
  60: { link: ['right_ring_finger3', 'right_ring_finger4'], id: 60, color: [255, 51, 51] },
  61: { link: ['right_hand_root', 'right_pinky_finger1'], id: 61, color: [0, 255, 0] },
  62: { link: ['right_pinky_finger1', 'right_pinky_finger2'], id: 62, color: [0, 255, 0] },
  63: { link: ['right_pinky_finger2', 'right_pinky_finger3'], id: 63, color: [0, 255, 0] },
  64: { link: ['right_pinky_finger3', 'right_pinky_finger4'], id: 64, color: [0, 255, 0] }
};

export const KeyPointMapping = {
    NOSE : 0,
    LEFT_EYE : 1,
    RIGHT_EYE : 2,
    LEFT_EAR : 3,
    RIGHT_EAR : 4,
    LEFT_SHOULDER : 5,
    RIGHT_SHOULDER : 6,
    LEFT_ELBOW : 7,
    RIGHT_ELBOW : 8,
    LEFT_WRIST : 9,
    RIGHT_WRIST : 10,
    LEFT_HIP : 11,
    RIGHT_HIP : 12,
    LEFT_KNEE : 13,
    RIGHT_KNEE : 14,
    LEFT_ANKLE : 15,
    RIGHT_ANKLE : 16,
  
    LEFT_BIG_TOE : 17,
    LEFT_SMALL_TOE : 18,
    LEFT_HEEL : 19,
    RIGHT_BIG_TOE : 20,
    RIGHT_SMALL_TOE : 21,
    RIGHT_HEEL : 22,

    
    FACE_START : 23,
    FACE_END : 90,
    LEFT_HAND_START : 91,
    // LEFT_HAND_END : 111,
    LEFT_HAND_WRIST : 91,
    LEFT_HAND_THUMB_1 : 92,
    LEFT_HAND_THUMB_2 : 93,
    LEFT_HAND_THUMB_3 : 94,
    LEFT_HAND_THUMB_4 : 95,
    LEFT_HAND_INDEX_1 : 96,
    LEFT_HAND_INDEX_2 : 97,
    LEFT_HAND_INDEX_3 : 98,
    LEFT_HAND_INDEX_4 : 99,
    LEFT_HAND_MIDDLE_1 : 100,
    LEFT_HAND_MIDDLE_2 : 101,
    LEFT_HAND_MIDDLE_3 : 102,
    LEFT_HAND_MIDDLE_4 : 103,
    LEFT_HAND_RING_1 : 104,
    LEFT_HAND_RING_2 : 105,
    LEFT_HAND_RING_3 : 106,
    LEFT_HAND_RING_4 : 107,
    LEFT_HAND_PINKY_1 : 108,
    LEFT_HAND_PINKY_2 : 109,
    LEFT_HAND_PINKY_3 : 110,
    LEFT_HAND_PINKY_4 : 111,
    RIGHT_HAND_START : 112,
    // RIGHT_HAND_END : 132,
    RIGHT_HAND_WRIST : 112,
    RIGHT_HAND_THUMB_1 : 113,
    RIGHT_HAND_THUMB_2 : 114,
    RIGHT_HAND_THUMB_3 : 115,
    RIGHT_HAND_THUMB_4 : 116,
    RIGHT_HAND_INDEX_1 : 117,
    RIGHT_HAND_INDEX_2 : 118,
    RIGHT_HAND_INDEX_3: 119,
    RIGHT_HAND_INDEX_4 : 120,
    RIGHT_HAND_MIDDLE_1 : 121,
    RIGHT_HAND_MIDDLE_2 : 122,
    RIGHT_HAND_MIDDLE_3 : 123,
    RIGHT_HAND_MIDDLE_4 : 124,
    RIGHT_HAND_RING_1 : 125,
    RIGHT_HAND_RING_2 : 126,
    RIGHT_HAND_RING_3 : 127,
    RIGHT_HAND_RING_4 : 128,
    RIGHT_HAND_PINKY_1 : 129,
    RIGHT_HAND_PINKY_2 : 130,
    RIGHT_HAND_PINKY_3 : 131,
    RIGHT_HAND_PINKY_4 : 132
  }

// 关键点名称到ID的映射
export const KEYPOINT_NAME_TO_ID = {
  'nose': 0,
  'left_eye': 1,
  'right_eye': 2,
  'left_ear': 3,
  'right_ear': 4,
  'left_shoulder': 5,
  'right_shoulder': 6,
  'left_elbow': 7,
  'right_elbow': 8,
  'left_wrist': 9,
  'right_wrist': 10,
  'left_hip': 11,
  'right_hip': 12,
  'left_knee': 13,
  'right_knee': 14,
  'left_ankle': 15,
  'right_ankle': 16,
  'left_big_toe': 17,
  'left_small_toe': 18,
  'left_heel': 19,
  'right_big_toe': 20,
  'right_small_toe': 21,
  'right_heel': 22,
  'left_hand_root': 91,
  'left_thumb1': 92,
  'left_thumb2': 93,
  'left_thumb3': 94,
  'left_thumb4': 95,
  'left_forefinger1': 96,
  'left_forefinger2': 97,
  'left_forefinger3': 98,
  'left_forefinger4': 99,
  'left_middle_finger1': 100,
  'left_middle_finger2': 101,
  'left_middle_finger3': 102,
  'left_middle_finger4': 103,
  'left_ring_finger1': 104,
  'left_ring_finger2': 105,
  'left_ring_finger3': 106,
  'left_ring_finger4': 107,
  'left_pinky_finger1': 108,
  'left_pinky_finger2': 109,
  'left_pinky_finger3': 110,
  'left_pinky_finger4': 111,
  'right_hand_root': 112,
  'right_thumb1': 113,
  'right_thumb2': 114,
  'right_thumb3': 115,
  'right_thumb4': 116,
  'right_forefinger1': 117,
  'right_forefinger2': 118,
  'right_forefinger3': 119,
  'right_forefinger4': 120,
  'right_middle_finger1': 121,
  'right_middle_finger2': 122,
  'right_middle_finger3': 123,
  'right_middle_finger4': 124,
  'right_ring_finger1': 125,
  'right_ring_finger2': 126,
  'right_ring_finger3': 127,
  'right_ring_finger4': 128,
  'right_pinky_finger1': 129,
  'right_pinky_finger2': 130,
  'right_pinky_finger3': 131,
  'right_pinky_finger4': 132
};

/**
 * 根据SKELETON_INFO生成连接数组，包含颜色信息
 * @returns {Array} 包含连接信息和颜色的数组
 */
export function getSkeletonConnectionsWithColors() {
  return Object.values(SKELETON_INFO).map(info => ({
    connection: [
      KEYPOINT_NAME_TO_ID[info.link[0]],
      KEYPOINT_NAME_TO_ID[info.link[1]]
    ],
    color: info.color,
    id: info.id
  }));
}