# 康复动作评估系统

## 概述

这是一个基于Vue 3的智能康复训练系统，能够实时评估用户的康复动作并提供反馈。系统使用COCO-WholeBody格式的133个关键点进行姿态检测，支持多种康复动作的评估。

## 核心功能

### 1. 动作评估引擎 (`useActionEvaluationEngine`)
- **位置**: `src/composables/useActionEvaluationEngine.js`
- **功能**: 根据动作类型、侧别和难度级别动态加载评估器
- **支持的动作**:
  - `shoulder_touch`: 对侧触肩
  - `arm_raise`: 手臂上举
  - `finger_touch`: 指尖对触（待实现）
  - `palm_flip`: 手掌翻转（待实现）

### 2. 增强训练会话管理 (`useEnhancedTrainingSession`)
- **位置**: `src/composables/useEnhancedTrainingSession.js`
- **功能**: 管理完整的训练流程，集成评估引擎和音频反馈
- **特性**:
  - 自动动作切换
  - 实时评估更新
  - 音频反馈集成
  - 训练数据收集

### 3. 训练数据管理器 (`trainingDataManager`)
- **位置**: `src/utils/trainingDataManager.js`
- **功能**: 格式化、保存和导出训练数据
- **支持格式**: JSON
- **存储方式**: 本地存储 + 文件下载

### 4. 音频反馈系统 (`useAudioFeedback`)
- **位置**: `src/composables/useAudioFeedback.js`
- **功能**: 提供语音指导和音效反馈
- **特性**:
  - 中文语音播报
  - 状态相关音效
  - 音量控制
  - 开关控制

## 动作检测器

### 已实现的检测器

#### 1. 肩膀触摸检测器 (`ShoulderTouchLogic`)
- **位置**: `src/composables/gemini/ShoulderTouchLogic.js`
- **状态机**: IDLE → MOVING_TO_TARGET → HOLDING → RETURNING → COMPLETED
- **评分**: 0-100分，基于动作完成度和保持时间

#### 2. 手臂上举检测器 (`ArmRaiseDetector`)
- **位置**: `src/composables/detectors/armRaiseDetector.js`
- **状态机**: IDLE → RAISING → HOLDING → LOWERING → COMPLETED
- **评分**: 基于抬起角度、保持时间和放下控制

### 待实现的检测器
- `fingerTouchDetector.js`: 指尖对触动作
- `palmFlipDetector.js`: 手掌翻转动作

## 使用方法

### 1. 基本使用

```javascript
import { useEnhancedTrainingSession } from '@/composables/useEnhancedTrainingSession'

const trainingSession = useEnhancedTrainingSession()

// 启动训练会话
trainingSession.startSession()

// 获取当前评估结果
const score = trainingSession.currentScore.value
const feedback = trainingSession.currentFeedback.value
const stage = trainingSession.currentActionStage.value
```

### 2. 在组件中使用

```vue
<template>
  <div class="training-interface">
    <!-- 分数显示 -->
    <div class="score">{{ currentScore }}</div>
    
    <!-- 反馈文本 -->
    <div class="feedback">{{ currentFeedback }}</div>
    
    <!-- 动作阶段指示器 -->
    <div :class="getActionStageStyle(currentActionStage)">
      {{ getActionStageText(currentActionStage) }}
    </div>
  </div>
</template>

<script setup>
import { useEnhancedTrainingSession } from '@/composables/useEnhancedTrainingSession'

const trainingSession = useEnhancedTrainingSession()

// 响应式数据
const currentScore = trainingSession.currentScore
const currentFeedback = trainingSession.currentFeedback
const currentActionStage = trainingSession.currentActionStage
</script>
```

### 3. 音频控制

```javascript
// 切换音频开关
trainingSession.toggleAudio()

// 设置音量 (0-1)
trainingSession.setAudioVolume(0.8)

// 检查音频状态
const isAudioEnabled = trainingSession.audioEnabled.value
```

## 数据格式

### 关键点数据格式
```javascript
// 133个关键点，每个点包含 [x, y, confidence]
const keypoints = [
  [0.5, 0.3, 0.9],  // 点0: [x坐标, y坐标, 置信度]
  [0.6, 0.4, 0.8],  // 点1
  // ... 131个更多的点
]
```

### 训练报告格式
```javascript
{
  session_info: {
    session_id: "session_1234567890",
    patient_id: "patient_001",
    start_time: "2024-01-01T10:00:00.000Z",
    end_time: "2024-01-01T10:15:00.000Z",
    duration_seconds: 900,
    total_actions: 4,
    completed_actions: 3,
    average_score: 85,
    status: "completed"
  },
  action_results: [
    {
      action_id: "action_1",
      action_type: "shoulder_touch",
      action_name: "对侧触肩",
      side: "left",
      difficulty_level: "medium",
      score: 92,
      status: "completed",
      duration_seconds: 45
    }
    // ... 更多动作结果
  ],
  summary: {
    completion_rate: 75,
    average_score: 85,
    highest_score: 95,
    lowest_score: 70,
    total_training_time: 900
  }
}
```

## 扩展指南

### 添加新的动作检测器

1. 创建检测器类：
```javascript
// src/composables/detectors/newActionDetector.js
export class NewActionDetector {
  constructor(side, level) {
    this.side = side
    this.level = level
    this.reset()
  }
  
  reset() {
    this.state = 'IDLE'
    this.score = 0
    this.feedback = '准备开始新动作'
  }
  
  update(keypoints) {
    // 实现检测逻辑
    return {
      success: true,
      state: this.state,
      score: this.score,
      feedback: this.feedback
    }
  }
}
```

2. 在评估引擎中注册：
```javascript
// src/composables/useActionEvaluationEngine.js
import NewActionDetector from '@/composables/detectors/newActionDetector'

// 在 loadDetector 方法中添加
case 'new_action':
  currentDetector.value = new NewActionDetector(side, level)
  break
```

3. 更新动作列表：
```javascript
// src/stores/training.js
const defaultActions = [
  // ... 现有动作
  {
    action_id: 'action_5',
    action_type: 'new_action',
    action_name: '新动作',
    side: 'left',
    difficulty_level: 'medium',
    video_url: '/videos/new_action.mp4'
  }
]
```

## 测试

### 运行测试
```javascript
// 在浏览器控制台中
import testEvaluationEngine from '@/test/evaluationEngineTest'
testEvaluationEngine()
```

### 测试覆盖
- 动作评估引擎基本功能
- 检测器加载和切换
- 评估结果更新
- 重置和停止功能

## 注意事项

1. **性能优化**: 评估更新频率建议控制在30fps以内
2. **错误处理**: 所有检测器都应处理关键点置信度不足的情况
3. **用户体验**: 音频反馈应适度，避免过于频繁的提示
4. **数据隐私**: 训练数据仅存储在本地，不会上传到服务器
5. **浏览器兼容**: 音频功能需要现代浏览器支持

## 故障排除

### 常见问题

1. **检测器加载失败**
   - 检查动作类型是否正确
   - 确认检测器文件是否存在

2. **音频不工作**
   - 检查浏览器音频权限
   - 确认音频是否被静音

3. **评估结果不准确**
   - 检查关键点数据质量
   - 调整检测器参数

4. **训练数据保存失败**
   - 检查本地存储空间
   - 确认浏览器支持localStorage
