/**
 * 工作流状态转换管理
 * 负责管理应用状态机和状态转换逻辑
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useWorkflowStore = defineStore('workflow', () => {
  // 当前状态
  const currentState = ref("waiting"); // 默认为waiting状态

  // 暂停状态相关
  const isPaused = ref(false);
  const pauseReason = ref(null);
  const pausedFromState = ref(null);

  // 状态转换定时器
  const stateTransitionTimer = ref(null);

  // 计算属性
  const canTransitionTo = computed(() => (targetState) => {
    const validTransitions = {
      start_failed: ["waiting"],
      waiting: ["introduction"],
      introduction: ["preparation", "waiting"],
      preparation: ["training", "waiting"],
      training: ["preparation", "reporting", "waiting"],
      reporting: ["waiting"],
    };

    return validTransitions[currentState.value]?.includes(targetState) || false;
  });

  const isInTrainingFlow = computed(() => {
    return ["introduction", "preparation", "training", "reporting"].includes(
      currentState.value
    );
  });

  /**
   * 状态转换方法
   */
  const transitionTo = (newState, delay = 0) => {
    // 清除之前的定时器
    if (stateTransitionTimer.value) {
      clearTimeout(stateTransitionTimer.value);
      stateTransitionTimer.value = null;
    }
    const executeTransition = () => {
      if (canTransitionTo.value(newState)) {
        const oldState = currentState.value;
        currentState.value = newState;
        console.log(`[Workflow] 状态转换: ${oldState} -> ${newState}`);
        return true;
      } else {
        console.warn(
          `[Workflow] 无效的状态转换: ${currentState.value} -> ${newState}`
        );
        return false;
      }
    };
    if (delay > 0) {
      console.log(`[Workflow] 计划在${delay}ms后转换到状态: ${newState}`);
      stateTransitionTimer.value = setTimeout(executeTransition, delay);
    } else {
      return executeTransition();
    }
  };

  /**
   * 转换到准备阶段
   */
  const transitionToPreparation = () => {
    return transitionTo("preparation");
  };

  /**
   * 转换到训练阶段
   */
  const transitionToTraining = () => {
    return transitionTo("training");
  };

  /**
   * 转换到报告阶段
   */
  const transitionToReporting = () => {
    return transitionTo("reporting");
  };
  /**
   * 转换到报告阶段
   */
  const transitionToWaiting = () => {
    return transitionTo("waiting");
  };
  /**
   * 转换到下一个动作的准备阶段
   */
  const transitionToNextActionPreparation = () => {
    if (currentState.value === "training") {
      return transitionTo("preparation");
    }
    return false;
  };

  /**
   * 暂停当前状态
   */
  const pauseWorkflow = (reason = null) => {
    if (!isPaused.value) {
      pausedFromState.value = currentState.value;
      pauseReason.value = reason;
      isPaused.value = true;
      console.log(`[Workflow] 工作流暂停，原因: ${reason}`);
    }
  };

  /**
   * 恢复工作流
   */
  const resumeWorkflow = () => {
    if (isPaused.value) {
      isPaused.value = false;
      pauseReason.value = null;
      pausedFromState.value = null;
      console.log("[Workflow] 工作流恢复");
    }
  };

  /**
   * 重置工作流状态
   */
  const resetWorkflow = () => {
    // 清除定时器
    if (stateTransitionTimer.value) {
      clearTimeout(stateTransitionTimer.value);
      stateTransitionTimer.value = null;
    }

    currentState.value = "waiting";
    isPaused.value = false;
    pauseReason.value = null;
    pausedFromState.value = null;

    console.log("[Workflow] 工作流重置");
  };

  /**
   * 强制设置状态（用于错误恢复）
   */
  const forceSetState = (state) => {
    console.log(`[Workflow] 强制设置状态: ${currentState.value} -> ${state}`);
    currentState.value = state;
  };

  /**
   * 取消计划的状态转换
   */
  const cancelScheduledTransition = () => {
    if (stateTransitionTimer.value) {
      clearTimeout(stateTransitionTimer.value);
      stateTransitionTimer.value = null;
      console.log("[Workflow] 取消计划的状态转换");
    }
  };

  return {
    // 响应式数据
    currentState,
    isPaused,
    pauseReason,
    pausedFromState,

    // 计算属性
    canTransitionTo,
    isInTrainingFlow,

    // 方法
    transitionTo,
    transitionToPreparation,
    transitionToTraining,
    transitionToReporting,
    transitionToWaiting,
    transitionToNextActionPreparation,
    pauseWorkflow,
    resumeWorkflow,
    resetWorkflow,
    forceSetState,
    cancelScheduledTransition,
  };
})
