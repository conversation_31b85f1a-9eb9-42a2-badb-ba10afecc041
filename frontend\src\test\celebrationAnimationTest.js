/**
 * 庆祝动画功能测试
 * 测试动作完成后的庆祝动画和状态转换流程
 */

// 模拟测试环境
const mockStores = {
  training: {
    currentAction: { action_name: '对侧触肩', action_id: 1 },
    currentActionIndex: 0,
    actionList: [
      { action_name: '对侧触肩', action_id: 1 },
      { action_name: '手臂上举', action_id: 2 }
    ],
    completeCurrentAction: (score) => {
      console.log(`[Mock Training] 完成动作，得分: ${score}`)
    },
    moveToNextAction: () => {
      console.log('[Mock Training] 切换到下一个动作')
      return true
    },
    isAllActionsCompleted: false
  },
  
  notification: {
    celebrationData: null,
    showCelebrationAnimation: false,
    triggerCelebrationAnimation: (data) => {
      console.log('[Mock Notification] 触发庆祝动画:', data)
      mockStores.notification.celebrationData = data
      mockStores.notification.showCelebrationAnimation = true
      return data
    },
    hideCelebrationAnimation: () => {
      console.log('[Mock Notification] 隐藏庆祝动画')
      mockStores.notification.showCelebrationAnimation = false
      mockStores.notification.celebrationData = null
    }
  },
  
  workflow: {
    transitionToNextActionPreparation: () => {
      console.log('[Mock Workflow] 转换到下一个动作准备状态')
    },
    transitionToReporting: () => {
      console.log('[Mock Workflow] 转换到报告状态')
    }
  }
}

// 模拟状态转换逻辑
const mockHandleActionComplete = (actionScore) => {
  console.log(`\n=== 开始处理动作完成 ===`)
  console.log(`动作得分: ${actionScore}`)
  
  // 完成当前动作
  mockStores.training.completeCurrentAction(actionScore)
  
  // 准备庆祝动画数据
  const currentAction = mockStores.training.currentAction
  const currentIndex = mockStores.training.currentActionIndex
  const totalActions = mockStores.training.actionList.length
  
  // 获取下一个动作名称
  let nextActionName = ''
  if (currentIndex < totalActions - 1) {
    const nextAction = mockStores.training.actionList[currentIndex + 1]
    nextActionName = nextAction ? nextAction.action_name : ''
  }
  
  // 触发庆祝动画
  if (currentAction) {
    mockStores.notification.triggerCelebrationAnimation({
      actionName: currentAction.action_name,
      score: actionScore,
      currentActionIndex: currentIndex,
      totalActions: totalActions,
      nextActionName: nextActionName,
      duration: 4000
    })
  }
  
  // 模拟动画播放时间
  console.log('庆祝动画播放中...')
  
  setTimeout(() => {
    console.log('庆祝动画完成，开始状态转换')
    
    // 检查是否还有下一个动作
    if (mockStores.training.isAllActionsCompleted) {
      mockStores.workflow.transitionToReporting()
    } else {
      const hasNext = mockStores.training.moveToNextAction()
      if (hasNext) {
        mockStores.workflow.transitionToNextActionPreparation()
      } else {
        mockStores.workflow.transitionToReporting()
      }
    }
    
    console.log(`=== 动作完成处理结束 ===\n`)
  }, 1000) // 缩短测试时间
}

// 测试庆祝动画组件数据
const testCelebrationData = () => {
  console.log('\n=== 测试庆祝动画数据 ===')
  
  const testData = {
    actionName: '对侧触肩',
    score: 85,
    currentActionIndex: 0,
    totalActions: 4,
    nextActionName: '手臂上举',
    duration: 3000
  }
  
  console.log('庆祝动画数据:', testData)
  
  // 验证数据完整性
  const requiredFields = ['actionName', 'score', 'currentActionIndex', 'totalActions']
  const missingFields = requiredFields.filter(field => testData[field] === undefined)
  
  if (missingFields.length === 0) {
    console.log('✅ 庆祝动画数据完整')
  } else {
    console.log('❌ 缺少必要字段:', missingFields)
  }
  
  console.log('=== 庆祝动画数据测试完成 ===\n')
}

// 运行测试
const runTests = () => {
  console.log('🎉 开始庆祝动画功能测试\n')
  
  // 测试1: 庆祝动画数据
  testCelebrationData()
  
  // 测试2: 动作完成流程
  console.log('测试场景1: 完成第一个动作')
  mockHandleActionComplete(85)
  
  setTimeout(() => {
    console.log('测试场景2: 完成最后一个动作')
    mockStores.training.isAllActionsCompleted = true
    mockHandleActionComplete(92)
  }, 2000)
  
  setTimeout(() => {
    console.log('\n🎉 庆祝动画功能测试完成')
  }, 4000)
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runTests,
    testCelebrationData,
    mockHandleActionComplete,
    mockStores
  }
} else {
  // 浏览器环境直接运行
  runTests()
}

console.log('庆祝动画测试模块已加载')
