/**
 * 重构后的Socket.IO通信服务
 * 使用模块化store架构，实现基于WebSocket的数据驱动架构
 */

import { io } from 'socket.io-client'
import { useConnectionStore } from '@/stores/connection'
import { useWorkflowStore } from '@/stores/workflow'
class WebSocketService {
  constructor() {
    this.socket = null
    this.url = 'http://localhost:5000'
    this.isConnected = false
    this.isConnecting = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000
    this.heartbeatInterval = 30000
    this.heartbeatTimer = null
    this.reconnectTimer = null
    this.store = null
    // 消息队列（连接断开时暂存消息）
    this.messageQueue = []
    console.log('WebSocket服务初始化完成')
  }

  /**
   * 初始化store引用
   */
  initStore() {
    if (!this.connectionStore || !this.workflowStore) {
      this.connectionStore = useConnectionStore()
      this.workflowStore = useWorkflowStore()
      console.log('WebSocket服务已集成模块化store')
    }
  }

  /**
   * 建立Socket.IO连接
   */
  async connect() {
    if (this.isConnected || this.isConnecting) {
      console.warn('WebSocket已连接或正在连接中')
      return Promise.resolve()
    }

    // 初始化store
    this.initStore()
    this.isConnecting = true

    return new Promise((resolve, reject) => {
      try {
        // 创建Socket.IO连接
        this.socket = io(this.url, {
          transports: ['websocket', 'polling'],
          timeout: 10000,
          reconnection: true,
          reconnectionAttempts: this.maxReconnectAttempts,
          reconnectionDelay: this.reconnectInterval
        })

        // 连接成功事件
        this.socket.on('connect', () => {
          console.log('WebSocket连接成功')
          this.isConnected = true
          this.isConnecting = false
          this.reconnectAttempts = 0
          // 更新store连接状态
          if (this.connectionStore) {
            this.connectionStore.setConnectionStatus(true)
          }
          if (this.workflowStore && this.workflowStore.currentState === 'start_failed') {
            this.workflowStore.transitionTo('waiting')
          }
          // 发送队列中的消息
          resolve()
        })

        // 统一消息处理 - 使用onAny监听所有消息
        this.socket.onAny((eventName, data) => {
          this._handleMessage(eventName, data)
        })

        // 连接断开事件
        this.socket.on('disconnect', (reason) => {
          console.log('WebSocket连接断开:', reason)
          this._handleDisconnect()
        })

        // 连接错误事件
        this.socket.on('connect_error', (error) => {
          console.error('WebSocket连接错误:', error)
          this.isConnecting = false
          // 更新store连接状态
          if (this.connectionStore) {
            this.connectionStore.setConnectionStatus(false, error.message)
          }
          if (this.workflowStore) {
            this.workflowStore.forceSetState('start_failed')
          }

          reject(error)
        })

        // 重连事件
        this.socket.on('reconnect', (attemptNumber) => {
          console.log(`WebSocket重连成功，尝试次数: ${attemptNumber}`)
          this.reconnectAttempts = attemptNumber
        })

        this.socket.on('reconnect_attempt', (attemptNumber) => {
          console.log(`WebSocket重连尝试: ${attemptNumber}`)
          this.reconnectAttempts = attemptNumber
        })

      } catch (error) {
        console.error('创建WebSocket连接失败:', error)
        this.isConnecting = false
        reject(error)
      }
    })
  }

  /**
   * 断开WebSocket连接
   */
  disconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }

    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }

    this.isConnected = false
    this.isConnecting = false
    this.reconnectAttempts = 0

    // 更新store连接状态
    if (this.connectionStore) {
      this.connectionStore.setConnectionStatus(false)
    }

    console.log('WebSocket连接已断开')
  }



  /**
   * 发送消息
   * @param {string} eventType - 事件类型
   * @param {object} data - 消息数据
   */
  send(eventType, data = {}) {
    if (!this.isConnected) {
      console.warn('WebSocket未连接，消息已加入队列')
      this.messageQueue.push({ eventType, data })
      return
    }

    try {
      this.socket.emit(eventType, data)
      console.log('发送消息:', { eventType, data })
    } catch (error) {
      console.error('发送消息失败:', error)
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
      queuedMessages: this.messageQueue.length
    }
  }

  /**
   * 处理接收到的消息 - 简化版本，直接分发到主store
   * @private
   */
  _handleMessage(messageType, data) {
   
    try {
      // console.log(`[WebSocket] 收到消息: ${messageType}`, data)
      // 分发到连接store处理实时数据
      if (this.connectionStore) {
        this.connectionStore.updateRealTimeData(data)
      } else {
        console.warn('连接store未初始化')
      }

    } catch (error) {
      console.error('处理WebSocket消息失败:', error, messageType, data)

      // 更新store错误状态
      if (this.connectionStore) {
        this.connectionStore.setConnectionStatus(this.isConnected, `消息处理错误: ${error.message}`)
      }
    }
  }

  /**
   * 处理连接断开
   * @private
   */
  _handleDisconnect() {
    this.isConnected = false
    this.isConnecting = false

    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }

    // 更新store连接状态
    if (this.connectionStore) {
      this.connectionStore.setConnectionStatus(false, '连接已断开')
    }

    // 尝试重连
    this._attemptReconnect()
  }

  /**
   * 尝试重连
   * @private
   */
  _attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('达到最大重连次数，停止重连')

      // 更新store连接状态
      if (this.connectionStore) {
        this.connectionStore.setConnectionStatus(false, `重连失败，已尝试${this.reconnectAttempts}次`)
      }
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectInterval * Math.pow(1.5, this.reconnectAttempts - 1)

    console.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连`)

    this.reconnectTimer = setTimeout(() => {
      this.connect().catch(error => {
        console.error(`第${this.reconnectAttempts}次重连失败:`, error)
      })
    }, delay)
  }

  /**
   * 发送队列中的消息
   * @private
   */

}

// 创建全局实例
export const websocketService = new WebSocketService()

// 便捷的发送消息函数
export const sendWebSocketMessage = (eventType, data = {}) => {
  return websocketService.send(eventType, data)
}

export default websocketService
