/**
 * ShoulderTouchLogic 集成测试
 * 验证 ShoulderTouchLogic 与 useSimplifiedTrainingSession 的集成
 */

import ShoulderTouchLogic from '@/composables/gemini/ShoulderTouchLogic'
import { utils } from '@/utils/geometryUtils'

// 模拟关键点数据（COCO-WholeBody 133个关键点）
function createMockKeypoints() {
  const keypoints = new Array(133).fill(null).map((_, index) => [
    Math.random(), // x 坐标 (0-1)
    Math.random(), // y 坐标 (0-1)
    Math.random() > 0.3 ? 0.8 : 0.2 // 置信度
  ])
  
  // 设置一些关键的关键点
  // 左肩膀 (11)
  keypoints[11] = [0.3, 0.4, 0.9]
  // 右肩膀 (12)
  keypoints[12] = [0.7, 0.4, 0.9]
  // 左手腕 (15)
  keypoints[15] = [0.2, 0.6, 0.9]
  // 右手腕 (16)
  keypoints[16] = [0.8, 0.6, 0.9]
  
  return keypoints
}

// 模拟摸左肩膀的动作序列
function createShoulderTouchSequence() {
  const sequences = []
  
  // 1. 初始状态 - 手在身体两侧
  sequences.push(createMockKeypoints())
  
  // 2. 手开始向肩膀移动
  const movingKeypoints = createMockKeypoints()
  movingKeypoints[16] = [0.6, 0.5, 0.9] // 右手腕向左肩膀移动
  sequences.push(movingKeypoints)
  
  // 3. 手接近肩膀
  const nearKeypoints = createMockKeypoints()
  nearKeypoints[16] = [0.35, 0.42, 0.9] // 右手腕接近左肩膀
  sequences.push(nearKeypoints)
  
  // 4. 手触摸肩膀
  const touchKeypoints = createMockKeypoints()
  touchKeypoints[16] = [0.3, 0.4, 0.9] // 右手腕触摸左肩膀
  sequences.push(touchKeypoints)
  
  // 5. 保持触摸
  for (let i = 0; i < 20; i++) { // 模拟保持2秒（20帧 * 100ms）
    const holdKeypoints = createMockKeypoints()
    holdKeypoints[16] = [0.3, 0.4, 0.9]
    sequences.push(holdKeypoints)
  }
  
  // 6. 手返回原位
  const returnKeypoints = createMockKeypoints()
  returnKeypoints[16] = [0.8, 0.6, 0.9]
  sequences.push(returnKeypoints)
  
  return sequences
}

// 测试函数
function testShoulderTouchIntegration() {
  console.log('=== ShoulderTouchLogic 集成测试开始 ===')
  
  // 1. 测试工具函数
  console.log('\n1. 测试几何工具函数:')
  const point1 = [0.3, 0.4, 0.9]
  const point2 = [0.35, 0.42, 0.8]
  const distance = utils.calculateDistance(point1, point2)
  console.log(`两点距离: ${distance.toFixed(4)}`)
  
  // 2. 测试 ShoulderTouchLogic 初始化
  console.log('\n2. 测试 ShoulderTouchLogic 初始化:')
  const shoulderTouch = new ShoulderTouchLogic('left', 'medium')
  console.log(`初始状态: ${shoulderTouch.state}`)
  console.log(`初始分数: ${shoulderTouch.score}`)
  
  // 3. 测试动作检测序列
  console.log('\n3. 测试动作检测序列:')
  const sequences = createShoulderTouchSequence()
  
  sequences.forEach((keypoints, index) => {
    const result = shoulderTouch.update(keypoints)
    console.log(`帧 ${index + 1}: 状态=${result.state}, 分数=${result.score}, 反馈=${result.feedback}`)
    
    // 如果完成，停止测试
    if (result.state === 'COMPLETED') {
      console.log('✅ 动作检测完成！')
      return
    }
  })
  
  // 4. 测试重置功能
  console.log('\n4. 测试重置功能:')
  shoulderTouch.reset()
  console.log(`重置后状态: ${shoulderTouch.state}`)
  console.log(`重置后分数: ${shoulderTouch.score}`)
  
  console.log('\n=== ShoulderTouchLogic 集成测试完成 ===')
}

// 导出测试函数
export { testShoulderTouchIntegration, createMockKeypoints, createShoulderTouchSequence }

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，可以通过控制台调用
  window.testShoulderTouchIntegration = testShoulderTouchIntegration
  console.log('测试函数已添加到 window.testShoulderTouchIntegration，可在控制台调用')
}
