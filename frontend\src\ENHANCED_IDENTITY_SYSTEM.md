# 增强身份验证系统

## 概述

基于用户需求，实现了一个精简但功能完整的身份验证系统，解决了原有系统的bug并增加了智能的阶段化处理逻辑。

## 核心修复

### 1. 修复patientId更新bug

**问题**：用户离开画面时，`connectionStore.patientId` 不会被清空，导致身份验证系统无法检测到用户消失。

**修复**：
```javascript
// 修复前
if(data.patient_id){
  patientId.value = data.patient_id;
}

// 修复后
patientId.value = data.patient_id || null;
```

### 2. 增强身份验证逻辑

**检测规则**：
- **用户消失**：3秒未检测到任何patientId → 暂停训练
- **代练检测**：3秒检测到不同patientId → 暂停训练并提示
- **用户返回**：检测到正确patientId → 自动恢复训练

## 阶段化超时处理

### 训练阶段 (60秒超时)
当用户在训练阶段消失超过60秒：
1. 保存当前动作记录（标记为`user_left_timeout`）
2. 标记剩余动作为未完成（标记为`user_left_incomplete`）
3. 生成训练报告
4. 自动跳转到报告页面

### 报告阶段 (30秒超时)
当用户在报告阶段消失超过30秒：
1. 直接清空所有数据
2. 返回登录页面等待下一个用户

### 其他阶段 (60秒超时)
当用户在其他阶段消失超过60秒：
1. 清空用户登录数据
2. 返回登录页面等待下一个用户

## 系统集成

### TrainingView.vue
- 启动身份验证监控
- 处理用户消失/代练/返回事件
- 显示相应的UI提示

### ReportView.vue
- 启动报告阶段的身份验证监控
- 30秒超时自动清理数据
- 保持1分钟备用清理机制

## 配置参数

```javascript
const DETECTION_TIMEOUT = 3000        // 3秒检测超时
const TRAINING_RESET_TIMEOUT = 60000  // 训练阶段60秒超时
const REPORT_RESET_TIMEOUT = 30000    // 报告阶段30秒超时
const OTHER_RESET_TIMEOUT = 60000     // 其他阶段60秒超时
```

## 使用示例

```javascript
// 启动身份验证
simpleIdentity.startVerification({
  onUserMissing: (data) => {
    // 用户消失处理
    showEmergencyPause('user_missing')
  },
  
  onUnauthorizedUser: (data) => {
    // 代练检测处理
    showEmergencyPause('unauthorized_user')
  },
  
  onUserReturned: (data) => {
    // 用户返回处理
    resumeTraining()
  },
  
  onResetToLogin: (data) => {
    // 超时重置处理
    if (data.type === 'training_timeout_to_report') {
      // 训练超时，已跳转报告页
    } else {
      // 其他情况，已返回登录页
    }
  }
})
```

## 错误处理

系统包含完善的错误处理机制：
- 训练记录保存失败时自动回退到登录页
- 路由跳转失败时提供备用处理
- 所有异常都会记录到控制台便于调试

## 测试

运行测试：
```javascript
// 在浏览器控制台中
import('/src/test-touchless-system.js').then(module => {
  module.testTouchlessSystem()
})
```

## 关键修复 (2024-12-19)

### 问题：用户消失后自动恢复训练
**现象**：用户消失后，系统显示10秒倒计时，倒计时结束后即使用户没有返回也会自动恢复训练。

**根本原因**：
1. `TrainingView.vue` 中 `EmergencyPause` 的 `recovery-delay` 设置为 `10000`（10秒）而不是 `60000`（60秒）
2. `auto-recovery` 设置为 `true`，导致倒计时结束后自动恢复训练
3. `handleEmergencyPauseRecovery` 函数没有对身份验证异常进行特殊处理

**修复方案**：
1. ✅ 将 `recovery-delay` 从 `10000` 改为 `60000`（60秒）
2. ✅ 将 `auto-recovery` 设置为 `false`，禁用自动恢复
3. ✅ 添加 `show-manual-actions="false"`，禁用手动操作按钮
4. ✅ 修改 `handleEmergencyPauseRecovery` 函数，对身份验证异常直接返回，不执行恢复

### 修复后的行为
- **用户消失3秒** → 暂停训练，显示身份验证异常界面，显示"XX秒后未返回画面将终止训练"
- **用户返回** → 立即隐藏异常界面，恢复训练
- **用户60秒未返回** → 身份验证系统的重置逻辑生效，保存训练记录并跳转报告页面
- **不允许手动恢复** → 身份验证异常状态下，用户无法通过任何手动操作恢复训练
- **报告页面检测** → 报告页面30秒后自动重置系统返回登录页

### 最新修复 (2024-12-19 补充)

**问题3**：函数调用错误 `trainingReportStore.addActionRecord is not a function`
- **修复**：将 `addActionRecord` 改为 `startActionRecord`

**问题4**：暂停画面没有显示倒计时信息
- **修复**：添加身份验证异常专用的倒计时显示，即使 `auto-recovery` 为 `false` 也显示倒计时

**问题5**：倒计时文案不明确
- **修复**：显示"XX秒后未返回画面将终止训练"而不是"自动恢复"

### 最新修复 (2024-12-19 第二轮)

**问题6**：用户消失后立即显示弹窗，而不是3秒后
- **原因**：身份验证启动时使用了 `{ immediate: true }`，导致立即检测
- **修复**：移除 `immediate: true`，增加5秒缓冲时间后开始首次检测

**问题7**：shoulderTouchLogic.js 报错 `Cannot read properties of undefined (reading '0')`
- **原因**：`getPoint` 函数没有检查 `keypoints[idx]` 是否存在
- **修复**：添加安全检查，返回默认值 `{ x: 0, y: 0, c: 0 }`

### 调试修复 (2024-12-19 第三轮)

**问题8**：expected 显示为 'unknown'，而不是正确的用户ID
- **现象**：`patientStore.userInfo?.patient_id` 的值为 `'unknown'`
- **调试方案**：
  1. 添加 `patientStore.userInfo` 的 watch 监听器，追踪变化
  2. 在 `handleValidationSuccess` 中添加详细调试信息
  3. 在 `validatePatientId` 中添加ID传递过程的调试
  4. 添加无效ID的检查和阻止逻辑

### 系统优化重构 (2024-12-19 第四轮)

**优化目标**：彻底重构 `useSimpleIdentityVerification.js`，消除代码冗余，简化逻辑

**主要改进**：
1. **简化状态管理**：
   - 合并相似状态变量
   - 统一定时器管理
   - 清晰的状态重置逻辑

2. **统一处理逻辑**：
   - 合并 `handleMissingUser` 和 `handleUnauthorizedUser` 为统一的 `handleIssue`
   - 合并 `triggerUserMissing` 和 `triggerUnauthorizedUser` 为统一的 `triggerIssueAction`
   - 简化防抖处理逻辑

3. **动态配置支持**：
   - 实现 `currentTimeoutConfig` 计算属性，根据当前阶段动态返回超时时间
   - EmergencyPause 组件的倒计时显示根据配置自动调整
   - 支持训练阶段10秒、报告阶段30秒、其他阶段60秒的差异化超时

4. **代码精简**：
   - 从原来的 500+ 行减少到 300+ 行
   - 移除重复的函数和逻辑
   - 统一的错误处理和回调机制

**技术改进**：
- 使用统一的 `clearAllTimers()` 函数管理所有定时器
- 简化的防抖机制，正确用户立即处理，其他情况500ms防抖
- 更清晰的函数命名和职责分离

## 优势

1. **精简高效**：移除了复杂的多阶段验证，保持核心功能
2. **智能处理**：根据不同阶段采取不同的超时策略
3. **用户友好**：训练中断时保存进度，避免数据丢失
4. **系统稳定**：完善的错误处理和备用机制
5. **易于维护**：代码结构清晰，逻辑简单明了
6. **严格控制**：身份验证异常只能通过用户返回或超时重置解决
