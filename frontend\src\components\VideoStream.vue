<!-- components/VideoStream.vue -->
<template>
  <div ref="containerRef" class="video-stream-wrapper w-full h-full relative">
    <!-- 加载状态 -->
    <div v-if="!hasVideoData" class="loading-state">
      <div class="flex flex-col items-center">
        <el-icon class="animate-spin text-2xl text-blue-500 mb-2"><Loading /></el-icon>
        <span class="text-gray-600 text-sm">等待视频数据...</span>
      </div>
    </div>

    <!-- 视频流 -->
    <img
      ref="imageRef"
      v-show="hasVideoData && !hasError"
      :src="currentFrameUrl"
      :alt="alt"
      @load="handleImageLoad"
      @error="handleImageError"
      class="camera-image w-full h-full object-contain"
    />

    <!-- 错误状态 -->
    <div v-if="hasError" class="error-state">
      <span class="text-red-600 text-center px-4">
        视频流连接失败
        <br>
        <span class="text-sm text-red-400">正在尝试恢复...</span>
      </span>
    </div>

    <!-- 人脸检测框 -->
    <div
      v-if="hasVideoData && faceBox && !hasError"
      class="absolute inset-0 pointer-events-none"
    >
      <div
        :style="faceBboxStyle"
        class="absolute border-2 rounded-lg transition-all duration-200 border-emerald-400 bg-emerald-400/10 shadow-[0_0_15px_rgba(52,211,153,0.4)]"
      >
        <div
          v-if="patientId"
          class="absolute -top-8 left-0 bg-emerald-500 text-white text-xs font-bold px-3 py-1 rounded"
        >
          ID: {{ patientId }}
        </div>
      </div>
    </div>

    <!-- 实时帧率显示 -->
    <div class="fps-display">
      <div class="bg-black/60 text-white text-xs font-mono px-2 py-1 rounded">
        FPS: {{ fps >= 0 ? Math.round(fps) : '--' }}
      </div>
    </div>

    <!-- 调试信息 -->
    <div v-if="showDebugInfo" class="debug-info">
      <div>FPS: {{ performanceStats.fps }}</div>
      <div>帧数: {{ frameCount }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onUnmounted, watch } from 'vue';
import { Loading } from '@element-plus/icons-vue';
import { storeToRefs } from 'pinia';
import { useConnectionStore } from '@/stores/connection';

const props = defineProps({
  alt: { type: String, default: '实时视频流' },
  showDebugInfo: { type: Boolean, default: false },
});

const emit = defineEmits(['load', 'error']);

const connectionStore = useConnectionStore();
const { frameData, frameCount, faceBox, patientId, fps } = storeToRefs(connectionStore);

const containerRef = ref(null);
const imageRef = ref(null); // 暴露内部的img元素
const hasError = ref(false);
const currentFrameUrl = ref('');

// 性能统计
let frameRenderCount = 0;
let fpsCalculationTime = Date.now();
const performanceStats = ref({ fps: 0 });

const hasVideoData = computed(() => frameData.value && frameData.value.length > 0);

// 计算人脸框样式 - 使用object-contain，直接百分比定位
const faceBboxStyle = computed(() => {
  if (!faceBox.value || faceBox.value.length < 4) {
    return { display: 'none' };
  }

  // 直接使用归一化坐标，因为object-contain确保图片完整显示
  let [x1, y1, x2, y2] = faceBox.value;

  // 添加适当的padding
  const width = x2 - x1;
  const height = y2 - y1;
  const paddingX = width * 0.1; // 左右各增加10%
  const paddingY = height * 0.05; // 上下各增加5%

  // 应用padding并确保坐标在有效范围内
  x1 = Math.max(0, x1 - paddingX);
  y1 = Math.max(0, y1 + height*0.15);
  x2 = Math.min(1, x2 + paddingX);
  y2 = Math.min(1, y2 - paddingY);

  // 检测是否在训练页面，如果是则添加偏移修正
  const isTrainingPage = window.location.pathname.includes('/training');
  const offsetCorrection = isTrainingPage ? 1 : 0; // 训练页面向右偏移3%

  // 转换为百分比
  const left = x1 * 100 + offsetCorrection;
  const top = y1 * 100;
  const boxWidth = (x2 - x1) * 100;
  const boxHeight = (y2 - y1) * 100;


  return {
    left: `${left}%`,
    top: `${top}%`,
    width: `${boxWidth}%`,
    height: `${boxHeight}%`,
  };
});

const handleImageLoad = () => {
  hasError.value = false;
  emit('load');
};

const handleImageError = () => {
  hasError.value = true;
  emit('error', { message: '视频帧加载失败' });
};

watch(frameData, (newFrameData) => {
  if (newFrameData) {
    currentFrameUrl.value = 'data:image/jpeg;base64,'+newFrameData;
    frameRenderCount++;
    const now = Date.now();
    if (now - fpsCalculationTime >= 1000) {
      performanceStats.value.fps = frameRenderCount;
      frameRenderCount = 0;
      fpsCalculationTime = now;
    }
  }
}, { immediate: true });

onUnmounted(() => {
 
});

// 暴露容器和图像的引用
defineExpose({
  containerRef,
  imageRef
});
</script>

<style scoped>
.video-stream-wrapper {
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
}
.camera-image {
  display: block;
  object-fit: cover; /* 保持 cover 以填满容器 */
}
.loading-state, .error-state {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.fps-display {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  pointer-events: none;
}

.debug-info {
  position: absolute;
  bottom: 8px;
  left: 8px;
  z-index: 10;
  pointer-events: none;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
}
</style>