/**
 * 训练数据管理器
 * 收集、格式化和保存训练数据为JSON格式
 */

/**
 * 格式化训练会话数据
 * @param {Object} sessionData - 训练会话数据
 * @param {Array} actionResults - 动作结果数组
 * @returns {Object} 格式化的训练数据
 */
export function formatTrainingData(sessionData, actionResults = []) {
  const timestamp = new Date().toISOString()
  
  return {
    // 会话基本信息
    session_info: {
      session_id: sessionData.session_id || `session_${Date.now()}`,
      patient_id: sessionData.patient_id || 'unknown',
      start_time: sessionData.start_time || timestamp,
      end_time: sessionData.end_time || timestamp,
      duration_seconds: sessionData.duration || 0,
      total_actions: sessionData.total_actions || actionResults.length,
      completed_actions: sessionData.completed_actions || actionResults.filter(a => a.status === 'completed').length,
      average_score: sessionData.average_score || 0,
      status: sessionData.status || 'completed'
    },
    
    // 动作详细结果
    action_results: actionResults.map(action => ({
      action_id: action.action_id,
      action_type: action.action_type,
      action_name: action.action_name,
      side: action.side || 'left',
      difficulty_level: action.difficulty_level || 'medium',
      score: action.score || 0,
      status: action.status || 'pending',
      start_time: action.start_time || null,
      end_time: action.end_time || null,
      duration_seconds: action.duration_seconds || 0,
      feedback_history: action.feedback_history || [],
      score_progression: action.score_progression || []
    })),
    
    // 统计摘要
    summary: {
      completion_rate: actionResults.length > 0 ? 
        Math.round((actionResults.filter(a => a.status === 'completed').length / actionResults.length) * 100) : 0,
      average_score: actionResults.length > 0 ? 
        Math.round(actionResults.reduce((sum, a) => sum + (a.score || 0), 0) / actionResults.length) : 0,
      highest_score: actionResults.length > 0 ? 
        Math.max(...actionResults.map(a => a.score || 0)) : 0,
      lowest_score: actionResults.length > 0 ? 
        Math.min(...actionResults.map(a => a.score || 0)) : 0,
      total_training_time: sessionData.duration || 0
    },
    
    // 元数据
    metadata: {
      export_time: timestamp,
      version: '1.0.0',
      system: 'digital-screen-rehabilitation',
      data_format: 'json'
    }
  }
}

/**
 * 保存训练数据到本地存储
 * @param {Object} trainingData - 格式化的训练数据
 * @param {string} storageKey - 存储键名
 */
export function saveToLocalStorage(trainingData, storageKey = 'training_sessions') {
  try {
    // 获取现有数据
    const existingSessions = JSON.parse(localStorage.getItem(storageKey) || '[]')
    
    // 添加新会话
    existingSessions.push(trainingData)
    
    // 保存回本地存储
    localStorage.setItem(storageKey, JSON.stringify(existingSessions))
    
    console.log('[TrainingDataManager] 训练数据已保存到本地存储')
    return true
  } catch (error) {
    console.error('[TrainingDataManager] 保存到本地存储失败:', error)
    return false
  }
}

/**
 * 保存训练数据到后端data目录
 * @param {Object} trainingData - 格式化的训练数据
 * @param {string} filename - 文件名（可选）
 */
export async function saveToLocalFile(trainingData, filename = null) {
  try {
    // 生成文件名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const defaultFilename = `training_session_${trainingData.session_info.patient_id}_${timestamp}.json`
    const finalFilename = filename || defaultFilename

    console.log('[TrainingDataManager] 开始保存训练数据到后端data目录:', finalFilename)

    // 调用后端API保存文件
    const response = await fetch('/api/training/save', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        filename: finalFilename,
        data: trainingData
      })
    })

    if (response.ok) {
      const result = await response.json()
      console.log('[TrainingDataManager] 训练数据已保存到后端data目录:', result)
      return {
        success: true,
        filename: finalFilename,
        method: 'backend_api',
        path: result.path || `data/${finalFilename}`
      }
    } else {
      const errorText = await response.text()
      console.error('[TrainingDataManager] 后端保存失败:', response.status, errorText)

      // 不降级到下载方式，避免弹窗，只保存到localStorage作为备份
      console.log('[TrainingDataManager] 后端保存失败，保存到localStorage作为备份')
      saveToLocalStorage(trainingData)

      return {
        success: false,
        filename: finalFilename,
        method: 'localStorage_fallback',
        error: `后端保存失败: ${response.status} ${errorText}`
      }
    }
  } catch (error) {
    console.error('[TrainingDataManager] 调用后端API失败:', error)

    // 不降级到下载方式，避免弹窗，只保存到localStorage作为备份
    console.log('[TrainingDataManager] API调用失败，保存到localStorage作为备份')
    saveToLocalStorage(trainingData)

    return {
      success: false,
      filename: filename || `training_session_${trainingData.session_info.patient_id}_${new Date().toISOString().replace(/[:.]/g, '-')}.json`,
      method: 'localStorage_fallback',
      error: `API调用失败: ${error.message}`
    }
  }
}

/**
 * 下载训练数据为JSON文件
 * @param {Object} trainingData - 格式化的训练数据
 * @param {string} filename - 文件名（可选）
 */
export function downloadAsJSON(trainingData, filename = null) {
  try {
    // 生成文件名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const defaultFilename = `training_session_${trainingData.session_info.patient_id}_${timestamp}.json`
    const finalFilename = filename || defaultFilename

    // 创建下载链接
    const dataStr = JSON.stringify(trainingData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)

    // 创建临时下载链接
    const link = document.createElement('a')
    link.href = url
    link.download = finalFilename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理URL对象
    URL.revokeObjectURL(url)

    console.log('[TrainingDataManager] 训练数据已下载为JSON文件:', finalFilename)
    return { success: true, filename: finalFilename, method: 'download' }
  } catch (error) {
    console.error('[TrainingDataManager] 下载JSON文件失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 从本地存储获取训练会话历史
 * @param {string} storageKey - 存储键名
 * @returns {Array} 训练会话数组
 */
export function getTrainingHistory(storageKey = 'training_sessions') {
  try {
    const sessions = JSON.parse(localStorage.getItem(storageKey) || '[]')
    return sessions
  } catch (error) {
    console.error('[TrainingDataManager] 获取训练历史失败:', error)
    return []
  }
}

/**
 * 获取后端保存的训练数据文件列表
 * @returns {Promise<Array>} 文件列表
 */
export async function getServerTrainingFiles() {
  try {
    const response = await fetch('/api/training/files', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })

    if (response.ok) {
      const files = await response.json()
      console.log('[TrainingDataManager] 获取服务器训练文件列表成功:', files)
      return files
    } else {
      console.error('[TrainingDataManager] 获取服务器文件列表失败:', response.status)
      return []
    }
  } catch (error) {
    console.error('[TrainingDataManager] 获取服务器文件列表时发生错误:', error)
    return []
  }
}

/**
 * 清理旧的训练数据（保留最近N个会话）
 * @param {number} keepCount - 保留的会话数量
 * @param {string} storageKey - 存储键名
 */
export function cleanupOldSessions(keepCount = 50, storageKey = 'training_sessions') {
  try {
    const sessions = getTrainingHistory(storageKey)

    if (sessions.length > keepCount) {
      // 按时间排序，保留最新的
      const sortedSessions = sessions.sort((a, b) =>
        new Date(b.session_info.start_time) - new Date(a.session_info.start_time)
      )

      const keepSessions = sortedSessions.slice(0, keepCount)
      localStorage.setItem(storageKey, JSON.stringify(keepSessions))

      console.log(`[TrainingDataManager] 已清理旧数据，保留最近${keepCount}个会话`)
    }
  } catch (error) {
    console.error('[TrainingDataManager] 清理旧数据失败:', error)
  }
}

/**
 * 生成训练报告摘要
 * @param {Object} trainingData - 训练数据
 * @returns {Object} 报告摘要
 */
export function generateReportSummary(trainingData) {
  const { session_info, action_results, summary } = trainingData
  
  return {
    patient_id: session_info.patient_id,
    session_date: new Date(session_info.start_time).toLocaleDateString('zh-CN'),
    total_actions: session_info.total_actions,
    completed_actions: session_info.completed_actions,
    completion_rate: `${summary.completion_rate}%`,
    average_score: summary.average_score,
    duration: `${Math.floor(session_info.duration_seconds / 60)}分${session_info.duration_seconds % 60}秒`,
    action_breakdown: action_results.map(action => ({
      name: action.action_name,
      score: action.score,
      status: action.status
    }))
  }
}

// 默认导出
export default {
  formatTrainingData,
  saveToLocalStorage,
  saveToLocalFile,
  downloadAsJSON,
  getTrainingHistory,
  getServerTrainingFiles,
  cleanupOldSessions,
  generateReportSummary
}
