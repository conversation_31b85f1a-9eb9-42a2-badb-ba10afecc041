/**
 * 对指动作检测器
 * 检测用户单手拇指依次与其他手指（食指、中指、无名指、小指）进行捏合的动作
 */

import { utils } from '@/utils/geometryUtils'
import { useTrainingReportStore } from '@/stores/trainingReport'

export class FingerTouchDetector {
  constructor(side = 'left', level = 'medium') {
    this.side = side // 根据side参数决定是哪只手的对指动作
    this.level = level

    // 设置关键点索引
    this.setupKeypoints()

    // 根据难度设置参数
    this.config = this.getDifficultyConfig(level)

    // 初始化报告存储
    this.reportStore = useTrainingReportStore()

    // 初始化状态
    this.reset()
  }

  setupKeypoints() {
    if (this.side === 'left') {
      // 左手对指：拇指尖依次与其他手指尖捏合
      this.thumbTipIdx = 95      // LEFT_HAND_THUMB_4 (拇指尖)
      this.indexTipIdx = 99      // LEFT_HAND_INDEX_4 (食指尖)
      this.middleTipIdx = 103    // LEFT_HAND_MIDDLE_4 (中指尖)
      this.ringTipIdx = 107      // LEFT_HAND_RING_4 (无名指尖)
      this.pinkyTipIdx = 111     // LEFT_HAND_PINKY_4 (小指尖)
      this.wristIdx = 9          // LEFT_WRIST
    } else {
      // 右手对指：拇指尖依次与其他手指尖捏合
      this.thumbTipIdx = 116     // RIGHT_HAND_THUMB_4 (拇指尖)
      this.indexTipIdx = 120     // RIGHT_HAND_INDEX_4 (食指尖)
      this.middleTipIdx = 124    // RIGHT_HAND_MIDDLE_4 (中指尖)
      this.ringTipIdx = 128      // RIGHT_HAND_RING_4 (无名指尖)
      this.pinkyTipIdx = 132     // RIGHT_HAND_PINKY_4 (小指尖)
      this.wristIdx = 10         // RIGHT_WRIST
    }

    // 手指序列：拇指依次与食指、中指、无名指、小指捏合
    this.fingerSequence = [
      { name: '食指', tipIdx: this.indexTipIdx },
      { name: '中指', tipIdx: this.middleTipIdx },
      { name: '无名指', tipIdx: this.ringTipIdx },
      { name: '小指', tipIdx: this.pinkyTipIdx }
    ]
  }
  
  getDifficultyConfig(level) {
    switch (level) {
      case 'easy':
        return {
          pinchThreshold: 0.035,      // 固定捏合阈值（归一化坐标）
          separationThreshold: 0.065, // 固定分离阈值（归一化坐标）
          holdDuration: 1000,        // 每次捏合保持时间（毫秒）
          requiredFingers: 4,        // 需要完成的手指数量
          minScorePerFinger: 15,     // 每个手指的最低得分
          perfectScoreThreshold: 85, // 完美完成的总分阈值
          fingerMultipliers: {       // 不同手指的难度系数
            0: 1.0,  // 食指（最容易）
            1: 1.1,  // 中指
            2: 1.2,  // 无名指
            3: 1.3   // 小指（最难）
          }
        }
      case 'hard':
        return {
          pinchThreshold: 0.03,      // 更严格的捏合要求（归一化坐标）
          separationThreshold: 0.12, // 更严格的分离要求（归一化坐标）
          holdDuration: 2000,        // 增加时间
          requiredFingers: 4,        // 需要完成所有手指
          minScorePerFinger: 20,
          perfectScoreThreshold: 95,
          fingerMultipliers: {
            0: 1.0,  // 食指
            1: 1.2,  // 中指
            2: 1.4,  // 无名指
            3: 1.6   // 小指
          }
        }
      case 'medium':
      default:
        return {
          pinchThreshold: 0.04,      // 中等捏合要求（归一化坐标）
          separationThreshold: 0.13, // 中等分离要求（归一化坐标）
          holdDuration: 1800,        // 增加时间
          requiredFingers: 3,        // 需要完成食指+中指+无名指
          minScorePerFinger: 18,
          perfectScoreThreshold: 90,
          fingerMultipliers: {
            0: 1.0,  // 食指
            1: 1.1,  // 中指
            2: 1.3,  // 无名指
            3: 1.5   // 小指
          }
        }
    }
  }
  
  reset() {
    this.state = 'IDLE'
    this.score = 0
    this.feedback = `准备${this.side === 'left' ? '左' : '右'}手对指动作：拇指依次与其他手指捏合`

    // 对指进度跟踪
    this.currentFingerIndex = 0  // 当前正在进行的手指索引
    this.completedFingers = []   // 已完成的手指记录
    this.fingerScores = []       // 每个手指的得分

    // 当前捏合状态
    this.holdStartTime = 0
    this.currentDistance = 0
    this.minDistanceReached = Infinity
    this.isCurrentFingerCompleted = false

    // 分离检测状态
    this.separationStartTime = 0
    this.minSeparationTime = 500     // 最小分离时间（毫秒）

    // 固定阈值（基于配置）
    this.pinchThreshold = this.config.pinchThreshold
    this.separationThreshold = this.config.separationThreshold
  }
  
  /**
   * 获取当前手指的动态阈值
   * @param {string} type - 阈值类型 ('pinch' 或 'separation')
   * @returns {number} 阈值
   */
  getCurrentFingerThreshold(type) {
    const baseThreshold = type === 'pinch' ? this.config.pinchThreshold : this.config.separationThreshold
    const multiplier = this.config.fingerMultipliers[this.currentFingerIndex] || 1.0

    return baseThreshold * multiplier
  }

  /**
   * 计算单个手指的捏合质量得分
   * @param {number} distance - 拇指与目标手指的距离
   * @param {number} holdTime - 保持时间
   * @returns {number} 得分 (0-25分)
   */
  calculateFingerScore(distance, holdTime) {
    // 距离得分 (0-15分)：距离越近得分越高
    const currentThreshold = this.getCurrentFingerThreshold('pinch')
    const maxDistance = currentThreshold * 2
    const distanceScore = Math.max(0, Math.min(15, 15 * (1 - distance / maxDistance)))

    // 保持时间得分 (0-10分)：保持时间越长得分越高
    const holdScore = Math.max(0, Math.min(10, 10 * (holdTime / this.config.holdDuration)))

    return distanceScore + holdScore
  }

  /**
   * 更新检测器状态
   * @param {Array} keypoints - 关键点数据
   * @returns {Object} 检测结果
   */
  update(keypoints) {
    if (this.state === 'COMPLETED') {
      return {
        success: true,
        state: this.state,
        score: this.score,
        feedback: this.feedback
      }
    }

    // 数据提取和验证
    const getPoint = (idx) => ({
      x: keypoints[idx][0],
      y: keypoints[idx][1],
      c: keypoints[idx][2]
    })

    const thumbTip = getPoint(this.thumbTipIdx)
    const wrist = getPoint(this.wristIdx)
    const middleTip = getPoint(this.middleTipIdx)

    // 获取所有手指尖位置
    const fingerTips = this.fingerSequence.map(finger => getPoint(finger.tipIdx))

    // 检查关键点是否在画面中
    if (utils.multipleOutOfBounds([thumbTip, wrist, middleTip, ...fingerTips])) {
      return {
        success: false,
        state: this.state,
        score: this.score,
        feedback: `请保证${this.side === 'left' ? '左' : '右'}手在画面中`,
      }
    }

    // 使用固定阈值，无需计算

    // 检查是否所有必需的手指都已完成
    if (this.currentFingerIndex >= this.config.requiredFingers) {
      this.state = 'COMPLETED'
      this.score = this.fingerScores.reduce((sum, score) => sum + score, 0)
      this.feedback = `对指动作完成！最终得分: ${Math.round(this.score)}`
      return {
        success: true,
        state: this.state,
        score: Math.round(this.score),
        feedback: this.feedback,
        completedFingers: this.completedFingers.length,
        totalFingers: this.config.requiredFingers
      }
    }

    // 获取当前目标手指
    const currentFinger = this.fingerSequence[this.currentFingerIndex]
    const currentFingerTip = getPoint(currentFinger.tipIdx)

    // 计算拇指与当前目标手指的距离
    this.currentDistance = utils.calculateDistance(thumbTip, currentFingerTip)
    console.log(`[FingerTouch] 当前距离: ${this.currentDistance}`)
    // 更新最小距离记录
    this.minDistanceReached = Math.min(this.minDistanceReached, this.currentDistance)

    // 获取当前手指的动态阈值
    const currentPinchThreshold = this.getCurrentFingerThreshold('pinch')
    const currentSeparationThreshold = this.getCurrentFingerThreshold('separation')

    // 状态机逻辑
    switch (this.state) {
      case 'IDLE':
        // 开始第一个手指的捏合
        this.state = 'APPROACHING'
        this.feedback = `请将拇指与${currentFinger.name}靠近并捏合`
        break

      case 'APPROACHING':
        // 检查是否达到捏合距离
        if (this.currentDistance <= currentPinchThreshold) {
          this.state = 'PINCHING'
          this.holdStartTime = Date.now()
          this.minDistanceReached = this.currentDistance
          this.feedback = `很好！请保持拇指与${currentFinger.name}的捏合`
        } else {
          this.feedback = `继续让拇指与${currentFinger.name}靠近... 距离: ${this.currentDistance.toFixed(3)} (目标: ${currentPinchThreshold.toFixed(3)})`
        }
        break

      case 'PINCHING':
        const holdingTime = Date.now() - this.holdStartTime

        // 检查是否仍在捏合距离内（允许一定的容错范围）
        if (this.currentDistance > currentPinchThreshold * 1.5) {
          this.state = 'APPROACHING'
          this.feedback = `请重新将拇指与${currentFinger.name}捏合`
          break
        }

        // 检查是否保持足够时间
        if (holdingTime >= this.config.holdDuration) {
          // 计算当前手指的得分
          const fingerScore = this.calculateFingerScore(this.minDistanceReached, holdingTime)
          this.fingerScores.push(fingerScore)
          this.completedFingers.push({
            name: currentFinger.name,
            score: fingerScore,
            distance: this.minDistanceReached,
            holdTime: holdingTime
          })

          // 记录当前手指完成
          this.reportStore.recordStageScore(
            `finger_${currentFinger.name}`,
            fingerScore,
            25, // 每个手指最大分数
            `${currentFinger.name}对指完成`
          )

          // 移动到下一个手指前，需要先分离
          this.isCurrentFingerCompleted = true

          if (this.currentFingerIndex < this.config.requiredFingers - 1) {
            // 还有下一个手指，进入分离状态
            this.state = 'SEPARATING'
            this.separationStartTime = Date.now()
            const nextFinger = this.fingerSequence[this.currentFingerIndex + 1]
            this.feedback = `${currentFinger.name}完成！请先分开拇指，然后与${nextFinger.name}捏合`
          } else {
            // 检查是否达到完成条件
            const totalScore = this.fingerScores.reduce((sum, score) => sum + score, 0)
            const avgScore = totalScore / this.fingerScores.length

            if (avgScore >= this.config.minScorePerFinger &&
                this.completedFingers.length >= this.config.requiredFingers) {
              this.state = 'COMPLETED'
              this.score = totalScore
              this.feedback = `对指动作完成！最终得分: ${Math.round(totalScore)}`
            } else {
              // 重新开始，分数不够
              this.currentFingerIndex = 0
              this.fingerScores = []
              this.completedFingers = []
              this.state = 'APPROACHING'
              this.feedback = '动作质量不够，请重新开始对指练习'
            }
          }
        } else {
          const remainingTime = Math.ceil((this.config.holdDuration - holdingTime) / 1000)
          this.feedback = `保持拇指与${currentFinger.name}捏合... 剩余 ${remainingTime} 秒`
        }
        break

      case 'SEPARATING':
        // 检查拇指是否已经分离
        if (this.currentDistance > currentSeparationThreshold) {
          const separationTime = Date.now() - this.separationStartTime

          if (separationTime >= this.minSeparationTime) {
            // 分离成功，移动到下一个手指
            this.currentFingerIndex++
            this.minDistanceReached = Infinity
            this.state = 'APPROACHING'

            const nextFinger = this.fingerSequence[this.currentFingerIndex]
            this.feedback = `分离成功！现在请将拇指与${nextFinger.name}捏合`

            console.log(`[FingerTouch] 成功分离，进入下一个手指: ${nextFinger.name}`)
          } else {
            const remainingTime = Math.ceil((this.minSeparationTime - separationTime) / 1000)
            this.feedback = `请保持分离状态... 剩余 ${remainingTime} 秒`
          }
        } else {
          this.feedback = `请将拇指分开，距离需要大于 ${currentSeparationThreshold.toFixed(3)} (当前: ${this.currentDistance.toFixed(3)})`
        }
        break
    }
    
    // 计算当前总分
    const currentTotalScore = this.fingerScores.reduce((sum, score) => sum + score, 0)

    // 记录实时反馈
    this.reportStore.recordFeedback(this.feedback, Math.round(currentTotalScore), this.state)

    return {
      success: true,
      state: this.state,
      score: Math.round(currentTotalScore),
      feedback: this.feedback,
      // 额外信息
      currentFinger: this.currentFingerIndex < this.fingerSequence.length ?
        this.fingerSequence[this.currentFingerIndex].name : '已完成',
      completedFingers: this.completedFingers.length,
      requiredFingers: this.config.requiredFingers,
      currentDistance: parseFloat(this.currentDistance.toFixed(4)),
      pinchThreshold: parseFloat(currentPinchThreshold.toFixed(4)),
      separationThreshold: parseFloat(currentSeparationThreshold.toFixed(4)),
      fingerScores: this.fingerScores,
      progress: `${this.completedFingers.length}/${this.config.requiredFingers}`
    }
  }
}

export default FingerTouchDetector
