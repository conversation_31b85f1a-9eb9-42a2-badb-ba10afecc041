/**
 * 训练报告相关的数据类型定义
 */

/**
 * 动作阶段评分记录
 * @typedef {Object} StageScore
 * @property {string} stage - 阶段名称 (如: 'raising', 'holding', 'lowering')
 * @property {number} score - 该阶段得分
 * @property {number} maxScore - 该阶段满分
 * @property {number} percentage - 完成百分比
 * @property {number} duration - 该阶段持续时间(毫秒)
 * @property {string} feedback - 该阶段反馈
 */

/**
 * 动作建议反馈
 * @typedef {Object} ActionAdvice
 * @property {string} category - 建议类别 ('excellent', 'good', 'needs_improvement', 'poor')
 * @property {string} overall - 总体评价
 * @property {Array<string>} strengths - 优点列表
 * @property {Array<string>} improvements - 改进建议列表
 * @property {Array<string>} tips - 练习技巧
 */

/**
 * 详细动作记录
 * @typedef {Object} DetailedActionRecord
 * @property {number} action_id - 动作ID
 * @property {string} action_type - 动作类型
 * @property {string} action_name - 动作名称
 * @property {string} side - 侧别
 * @property {string} difficulty_level - 难度等级
 * @property {number} final_score - 最终得分
 * @property {string} status - 状态
 * @property {string} start_time - 开始时间
 * @property {string} end_time - 结束时间
 * @property {number} duration_seconds - 持续时间
 * @property {Array<StageScore>} stage_scores - 阶段评分详情
 * @property {ActionAdvice} advice - 动作建议
 * @property {Array<Object>} feedback_timeline - 反馈时间线
 * @property {Object} performance_metrics - 性能指标
 */

/**
 * 训练会话总体评价
 * @typedef {Object} OverallAssessment
 * @property {string} level - 总体水平 ('excellent', 'good', 'fair', 'needs_improvement')
 * @property {number} overall_score - 总体得分
 * @property {string} summary - 总结
 * @property {Array<string>} achievements - 成就列表
 * @property {Array<string>} focus_areas - 需要关注的领域
 * @property {Array<string>} next_goals - 下次训练目标
 */

/**
 * 完整训练报告
 * @typedef {Object} TrainingReport
 * @property {Object} session_info - 会话基本信息
 * @property {Object} patient_info - 患者信息
 * @property {Array<DetailedActionRecord>} detailed_actions - 详细动作记录
 * @property {OverallAssessment} overall_assessment - 总体评价
 * @property {Object} statistics - 统计数据
 * @property {Object} metadata - 元数据
 */

// 动作建议模板
export const ACTION_ADVICE_TEMPLATES = {
  shoulder_touch: {
    excellent: {
      overall: "肩部触摸动作完成得非常出色！",
      strengths: ["动作流畅自然", "触摸精准到位", "返回控制良好"],
      improvements: [],
      tips: ["保持这种良好的动作模式", "可以尝试增加动作速度"]
    },
    good: {
      overall: "肩部触摸动作完成得很好！",
      strengths: ["基本动作正确", "能够完成触摸"],
      improvements: ["可以提高动作的流畅性", "注意返回时的控制"],
      tips: ["练习时保持肩部放松", "注意动作的节奏感"]
    },
    needs_improvement: {
      overall: "肩部触摸动作需要进一步练习",
      strengths: ["能够理解动作要求"],
      improvements: ["提高手臂的活动范围", "加强肩部灵活性", "改善动作协调性"],
      tips: ["每天进行肩部热身运动", "循序渐进增加练习强度", "注意动作的准确性"]
    },
    poor: {
      overall: "肩部触摸动作需要重点关注和练习",
      strengths: ["积极参与训练"],
      improvements: ["基础肩部活动能力需要提升", "动作协调性需要加强", "建议增加辅助练习"],
      tips: ["从简单的肩部活动开始", "可以寻求专业指导", "坚持每日练习"]
    }
  },
  
  arm_raise: {
    excellent: {
      overall: "手臂上举动作表现优秀！",
      strengths: ["上举幅度充分", "动作控制稳定", "肘关节伸展良好"],
      improvements: [],
      tips: ["继续保持良好的动作质量", "可以尝试负重练习"]
    },
    good: {
      overall: "手臂上举动作完成得不错！",
      strengths: ["能够完成上举动作", "基本姿态正确"],
      improvements: ["可以进一步提高上举高度", "加强动作的稳定性"],
      tips: ["注意保持躯干稳定", "练习时控制动作速度"]
    },
    needs_improvement: {
      overall: "手臂上举动作有待改善",
      strengths: ["有上举的意识"],
      improvements: ["增加肩关节活动范围", "提高手臂力量", "改善动作协调"],
      tips: ["进行肩部拉伸练习", "逐步增加上举幅度", "注意呼吸配合"]
    },
    poor: {
      overall: "手臂上举动作需要重点训练",
      strengths: ["参与训练的积极性"],
      improvements: ["基础肩关节功能需要恢复", "手臂力量需要加强", "建议专业康复指导"],
      tips: ["从被动活动开始", "配合物理治疗", "耐心坚持练习"]
    }
  },
  
  finger_touch: {
    excellent: {
      overall: "对指动作精准流畅！",
      strengths: ["手指协调性优秀", "对指精确", "动作节奏良好"],
      improvements: [],
      tips: ["保持手指的灵活性", "可以尝试更快的节奏"]
    },
    good: {
      overall: "对指动作完成得很好！",
      strengths: ["能够完成对指动作", "手指控制基本准确"],
      improvements: ["提高动作的精确性", "加强手指力量"],
      tips: ["练习精细动作控制", "注意手指的独立性"]
    },
    needs_improvement: {
      overall: "对指动作需要更多练习",
      strengths: ["理解动作要求"],
      improvements: ["提高手指灵活性", "改善精细动作控制", "加强手指协调"],
      tips: ["进行手指操练习", "使用握力球训练", "练习精细动作"]
    },
    poor: {
      overall: "对指动作需要基础训练",
      strengths: ["有训练的意愿"],
      improvements: ["基础手指功能需要恢复", "精细动作能力需要重建", "建议专业手功能训练"],
      tips: ["从简单的手指活动开始", "配合作业治疗", "坚持日常练习"]
    }
  },
  
  palm_flip: {
    excellent: {
      overall: "手掌翻转动作非常标准！",
      strengths: ["翻转幅度充分", "动作流畅", "节奏控制良好"],
      improvements: [],
      tips: ["保持动作的连贯性", "可以尝试增加翻转速度"]
    },
    good: {
      overall: "手掌翻转动作表现良好！",
      strengths: ["能够完成翻转", "基本动作正确"],
      improvements: ["增加翻转幅度", "提高动作流畅性"],
      tips: ["注意前臂的旋转", "保持手腕稳定"]
    },
    needs_improvement: {
      overall: "手掌翻转动作需要改进",
      strengths: ["有翻转的动作"],
      improvements: ["增加前臂旋转范围", "提高动作协调性", "加强腕关节灵活性"],
      tips: ["进行前臂旋转练习", "加强腕关节活动", "注意动作的完整性"]
    },
    poor: {
      overall: "手掌翻转动作需要基础训练",
      strengths: ["参与训练"],
      improvements: ["前臂旋转功能需要恢复", "腕关节活动需要改善", "建议专业康复训练"],
      tips: ["从被动旋转开始", "配合物理治疗", "循序渐进练习"]
    }
  }
}

// 总体评价等级定义
export const OVERALL_ASSESSMENT_LEVELS = {
  excellent: { min: 90, label: "优秀", color: "#52c41a" },
  good: { min: 75, label: "良好", color: "#1890ff" },
  fair: { min: 60, label: "一般", color: "#faad14" },
  needs_improvement: { min: 0, label: "需要改进", color: "#ff4d4f" }
}

export default {
  ACTION_ADVICE_TEMPLATES,
  OVERALL_ASSESSMENT_LEVELS
}
