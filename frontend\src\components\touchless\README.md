# 精简无接触异常处理系统

本系统为数字康复训练平台提供精简但完整的无接触异常处理能力，包括智能超时检测和身份验证。

## 系统组件

### 1. 核心Composables

#### `useTouchlessActionTimeout.js`
- **功能**: 基于时间的智能超时检测
- **特性**: 90秒超时、自动跳过、进度监测
- **回调**: `onTimeout`, `onWarning`, `onStagnation`

#### `useSimpleIdentityVerification.js` (新)
- **功能**: 精简身份验证系统
- **特性**:
  - 3秒检测超时（用户消失/代练检测）
  - 1分钟重置超时（返回登录页）
  - 自动暂停/恢复训练
- **回调**: `onUserMissing`, `onUnauthorizedUser`, `onUserReturned`, `onResetToLogin`

### 3. UI组件

#### `TimeoutWarning.vue`
- **功能**: 超时警告显示
- **特性**: 圆形倒计时、进度条、自动隐藏

#### `ActionSkipNotice.vue`
- **功能**: 动作跳过通知
- **特性**: 跳过原因显示、持续时间格式化、自动消失

#### `EmergencyPause.vue`
- **功能**: 紧急暂停覆盖层
- **特性**: 紧急类型分类、状态指示器、自动恢复

## 集成说明

### TrainingView.vue 集成
系统已完全集成到主训练界面：

1. **初始化**: 在 `onMounted` 中启动所有检测系统
2. **数据流**: 姿态数据自动传递给各检测系统
3. **UI显示**: 异常UI组件覆盖在训练界面上
4. **清理**: 在 `onUnmounted` 中清理所有资源

### useEnhancedTrainingSession.js 集成
训练会话管理已集成超时检测：

1. **启动检测**: 每个动作开始时自动启动超时检测
2. **进度更新**: 评估引擎分数变化时更新超时进展
3. **自动跳过**: 超时时自动跳过动作并显示通知
4. **手动控制**: 提供手动跳过和重启方法

## 使用方法


## 异常处理流程

### 1. 超时异常
1. 动作开始 → 启动超时检测
2. 60秒 → 显示超时警告
3. 90秒 → 自动跳过动作
4. 显示跳过通知 → 继续下一动作

### 2. 身份异常

#### 用户消失
1. 3秒未检测到用户 → 暂停训练并显示提示
2. 1分钟用户未返回 → 重置训练返回登录页
3. 用户返回 → 自动恢复训练

#### 代练检测
1. 3秒检测到不同用户ID → 暂停训练并显示代练警告
2. 1分钟登录用户未返回 → 重置训练返回登录页
3. 登录用户返回 → 自动恢复训练

## 使用方法

### 在TrainingView中集成
```javascript
import { useSimpleIdentityVerification } from '@/composables/useSimpleIdentityVerification'
import { useTouchlessActionTimeout } from '@/composables/useTouchlessActionTimeout'

// 启动身份验证
simpleIdentity.startVerification({
  onUserMissing: (data) => showEmergencyPause('user_missing'),
  onUnauthorizedUser: (data) => showEmergencyPause('unauthorized_user'),
  onUserReturned: () => resumeTraining(),
  onResetToLogin: () => redirectToLogin()
})

// 启动超时检测
touchlessTimeout.startDetection(action, {
  onTimeout: (data) => skipAction(data),
  onWarning: (data) => showTimeoutWarning(data)
})
```

## 配置选项

### 身份验证配置
- `DETECTION_TIMEOUT`: 3000ms (3秒检测超时)
- `RESET_TIMEOUT`: 60000ms (1分钟重置超时)

### 超时检测配置
- `maxAttemptTime`: 90000ms (90秒总超时)
- `warningTime`: 60000ms (60秒警告时间)
- `progressStagnation`: 30000ms (30秒停滞检测)
