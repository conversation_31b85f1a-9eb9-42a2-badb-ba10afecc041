<template>
  <div class="emergency-pause-overlay" v-if="isVisible">
    <!-- 紧急背景 -->
    <div class="emergency-backdrop"></div>
    
    <!-- 紧急内容 -->
    <div class="emergency-content">
      <!-- 紧急图标 -->
      <div class="emergency-icon">
        <div class="icon-container">
          <svg class="emergency-svg" viewBox="0 0 24 24" fill="none">
            <path d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>
      
      <!-- 紧急信息 -->
      <div class="emergency-info">
        <h2 class="emergency-title">{{ getEmergencyTitle() }}</h2>
        <p class="emergency-message">{{ getEmergencyMessage() }}</p>
        
        <!-- 紧急详情 -->
        <div class="emergency-details" v-if="showDetails">
          <div class="detail-card">
            <div class="detail-header">
              <span class="detail-icon">⚠️</span>
              <span class="detail-title">异常类型</span>
            </div>
            <div class="detail-content">{{ getExceptionTypeText() }}</div>
          </div>
          
          <div class="detail-card" v-if="emergencyData.gesture">
            <div class="detail-header">
              <span class="detail-icon">👋</span>
              <span class="detail-title">检测到手势</span>
            </div>
            <div class="detail-content">{{ emergencyData.gesture.config?.name || '未知手势' }}</div>
          </div>
          
          <div class="detail-card" v-if="emergencyData.action">
            <div class="detail-header">
              <span class="detail-icon">🎯</span>
              <span class="detail-title">当前动作</span>
            </div>
            <div class="detail-content">{{ emergencyData.action.action_name || '无' }}</div>
          </div>
        </div>
        
        <!-- 状态指示器 -->
        <div class="status-indicator">
          <div class="status-dot" :class="getStatusClass()"></div>
          <span class="status-text">{{ getStatusText() }}</span>
        </div>
        
        <!-- 自动恢复倒计时 -->
        <div class="auto-recovery" v-if="autoRecovery && countdown > 0">
          <div class="recovery-info">
            <p class="recovery-text">系统将在 {{ countdown }} 秒后自动恢复</p>
            <div class="recovery-progress">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{ width: progressPercentage + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 身份验证异常倒计时 -->
        <div class="identity-timeout" v-if="emergencyData.type === 'identity_exception' && countdown > 0">
          <div class="timeout-info">
            <p class="timeout-text">{{ countdown }} 秒后未返回画面将终止训练</p>
            <div class="timeout-progress">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{ width: progressPercentage + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 操作提示 -->
        <div class="action-hints">
          <div class="hint-item" v-for="hint in getActionHints()" :key="hint.id">
            <span class="hint-icon">{{ hint.icon }}</span>
            <span class="hint-text">{{ hint.text }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 紧急状态指示器 -->
    <div class="emergency-indicators">
      <div class="indicator-item" v-for="indicator in statusIndicators" :key="indicator.type">
        <div class="indicator-dot" :class="indicator.class"></div>
        <span class="indicator-label">{{ indicator.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  emergencyData: {
    type: Object,
    required: true,
    default: () => ({
      type: 'emergency_stop',
      reason: 'gesture_detected',
      timestamp: Date.now()
    })
  },
  autoRecovery: {
    type: Boolean,
    default: false
  },
  recoveryDelay: {
    type: Number,
    default: 10000 // 10秒自动恢复
  },
  showDetails: {
    type: Boolean,
    default: true
  },
  showManualActions: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['hide', 'recovery', 'manual-action'])

// 响应式数据
const isVisible = ref(true)
const countdown = ref(Math.ceil(props.recoveryDelay / 1000))
const countdownTimer = ref(null)
const recoveryTimer = ref(null)

// 计算属性
const progressPercentage = computed(() => {
  if (!props.autoRecovery) return 0
  const totalSeconds = Math.ceil(props.recoveryDelay / 1000)
  return ((totalSeconds - countdown.value) / totalSeconds) * 100
})

const statusIndicators = computed(() => {
  const indicators = []
  
  // 基础状态指示器
  indicators.push({
    type: 'emergency',
    class: 'emergency-active',
    label: '紧急状态'
  })
  
  // 根据异常类型添加指示器
  switch (props.emergencyData.type) {
    case 'emergency_stop':
      indicators.push({
        type: 'gesture',
        class: 'gesture-detected',
        label: '手势检测'
      })
      break
      
    case 'identity_exception':
      indicators.push({
        type: 'identity',
        class: 'identity-error',
        label: '身份异常'
      })
      break
      
    case 'action_timeout':
      indicators.push({
        type: 'timeout',
        class: 'timeout-error',
        label: '动作超时'
      })
      break
  }
  
  return indicators
})

// 方法
const getEmergencyTitle = () => {
  const titleMap = {
    'emergency_stop': '紧急停止',
    'request_help': '请求帮助',
    'restart_training': '重新开始',
    'identity_exception': '身份验证异常',
    'action_timeout': '动作超时',
    'system_error': '系统错误'
  }
  
  return titleMap[props.emergencyData.type] || '训练暂停'
}

const getEmergencyMessage = () => {
  const messageMap = {
    'emergency_stop': '检测到停止手势，训练已暂停',
    'request_help': '检测到求助手势，等待协助',
    'restart_training': '检测到重新开始手势，准备重启',
    'identity_exception': getIdentityExceptionMessage(),
    'action_timeout': '动作执行超时，训练已暂停',
    'system_error': '系统出现错误，训练已暂停'
  }

  return messageMap[props.emergencyData.type] || '训练已暂停，请等待处理'
}

const getIdentityExceptionMessage = () => {
  const reason = props.emergencyData.reason
  const reasonMap = {
    'user_missing': '未检测到用户，训练已暂停。请回到摄像头前继续训练。',
    'unauthorized_user': '检测到其他用户，训练已暂停。请确保只有登录用户在进行训练。',
    'identity_lost': '身份验证丢失，训练已暂停',
    'unauthorized_person': '检测到未授权人员，训练已暂停',
    'multiple_persons': '检测到多人，训练已暂停'
  }

  return reasonMap[reason] || '身份验证出现异常，请重新验证'
}

const getExceptionTypeText = () => {
  const typeMap = {
    'emergency_stop': '手势控制停止',
    'request_help': '用户求助',
    'restart_training': '重新开始请求',
    'identity_exception': '身份验证失败',
    'action_timeout': '动作执行超时',
    'system_error': '系统内部错误'
  }
  
  return typeMap[props.emergencyData.type] || '未知异常'
}

const getStatusClass = () => {
  const classMap = {
    'emergency_stop': 'status-error',
    'request_help': 'status-warning',
    'restart_training': 'status-info',
    'identity_exception': 'status-warning',
    'action_timeout': 'status-error',
    'system_error': 'status-error'
  }
  
  return classMap[props.emergencyData.type] || 'status-error'
}

const getStatusText = () => {
  const statusMap = {
    'emergency_stop': '已停止',
    'request_help': '等待帮助',
    'restart_training': '准备重启',
    'identity_exception': '验证中',
    'action_timeout': '已超时',
    'system_error': '错误状态'
  }
  
  return statusMap[props.emergencyData.type] || '暂停中'
}

const getActionHints = () => {
  const hintsMap = {
    'emergency_stop': [
      { id: 1, icon: '✋', text: '举起双手继续暂停' },
      { id: 2, icon: '👋', text: '放下双手恢复训练' }
    ],
    'request_help': [
      { id: 1, icon: '🆘', text: '等待工作人员协助' },
      { id: 2, icon: '👋', text: '挥手表示需要帮助' }
    ],
    'restart_training': [
      { id: 1, icon: '🔄', text: '系统正在重新初始化' },
      { id: 2, icon: '⏳', text: '请稍等片刻' }
    ],
    'identity_exception': [
      { id: 1, icon: '👤', text: '请确保只有患者在镜头前' },
      { id: 2, icon: '📷', text: '保持面部清晰可见' }
    ],
    'action_timeout': [
      { id: 1, icon: '⏰', text: '动作执行时间过长' },
      { id: 2, icon: '🎯', text: '系统将自动跳过此动作' }
    ]
  }
  
  return hintsMap[props.emergencyData.type] || [
    { id: 1, icon: '⏸️', text: '训练已暂停' },
    { id: 2, icon: '🔄', text: '等待系统处理' }
  ]
}

const startCountdown = () => {
  // 对于身份验证异常，即使 autoRecovery 为 false 也要显示倒计时
  const shouldShowCountdown = props.autoRecovery || props.emergencyData.type === 'identity_exception'
  if (!shouldShowCountdown) return

  countdownTimer.value = setInterval(() => {
    countdown.value--

    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value)
      // 只有 autoRecovery 为 true 时才触发自动恢复
      if (props.autoRecovery) {
        handleAutoRecovery()
      }
    }
  }, 1000)
}

const handleAutoRecovery = () => {
  console.log('[EmergencyPause] 自动恢复触发')
  emit('recovery', {
    type: 'auto_recovery',
    emergencyType: props.emergencyData.type
  })
  hideEmergency()
}

const hideEmergency = () => {
  isVisible.value = false
  emit('hide')
}

const cleanup = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
  
  if (recoveryTimer.value) {
    clearTimeout(recoveryTimer.value)
    recoveryTimer.value = null
  }
}

// 生命周期
onMounted(() => {
  console.log('[EmergencyPause] 紧急暂停显示:', props.emergencyData)

  // 对于身份验证异常，即使 autoRecovery 为 false 也要启动倒计时显示
  if (props.autoRecovery || props.emergencyData.type === 'identity_exception') {
    startCountdown()
  }
})

onUnmounted(() => {
  cleanup()
})
</script>

<style scoped>
.emergency-pause-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1500;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  animation: emergencyFadeIn 0.3s ease-out;
}

.emergency-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(220, 38, 38, 0.2);
  backdrop-filter: blur(8px);
  animation: backdropPulse 2s ease-in-out infinite;
}

.emergency-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.95), rgba(185, 28, 28, 0.95));
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow: 0 25px 50px rgba(220, 38, 38, 0.4);
  border: 3px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(15px);
  max-width: 500px;
  text-align: center;
  animation: emergencyPulse 1.5s ease-in-out infinite;
}

.emergency-icon {
  margin-bottom: 2rem;
}

.icon-container {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: iconShake 0.8s ease-in-out infinite;
}

.emergency-svg {
  width: 50px;
  height: 50px;
  color: white;
}

.emergency-info {
  color: white;
  width: 100%;
}

.emergency-title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
}

.emergency-message {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.95;
  line-height: 1.5;
}

.emergency-details {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;
}

.detail-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  text-align: left;
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.detail-icon {
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

.detail-title {
  font-weight: 600;
  font-size: 0.9rem;
}

.detail-content {
  font-size: 0.85rem;
  opacity: 0.9;
  padding-left: 1.7rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 0.5rem;
  animation: statusBlink 1s ease-in-out infinite;
}

.status-dot.status-error {
  background: #fca5a5;
}

.status-dot.status-warning {
  background: #fde047;
}

.status-dot.status-info {
  background: #93c5fd;
}

.status-text {
  font-weight: 500;
  font-size: 0.9rem;
}

.auto-recovery {
  margin-bottom: 2rem;
}

.recovery-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
}

.identity-timeout {
  margin-bottom: 2rem;
}

.timeout-info {
  background: rgba(255, 193, 7, 0.15);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 12px;
  padding: 1rem;
}

.timeout-text {
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #ffc107;
}

.timeout-progress {
  width: 100%;
}

.recovery-text {
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.recovery-progress {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.6));
  transition: width 1s linear;
}

.action-hints {
  display: grid;
  gap: 0.5rem;
}

.hint-item {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.85rem;
  opacity: 0.9;
}

.hint-icon {
  margin-right: 0.5rem;
  font-size: 1rem;
}

.emergency-indicators {
  position: absolute;
  top: 2rem;
  right: 2rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.indicator-item {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  backdrop-filter: blur(5px);
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 0.5rem;
  animation: indicatorPulse 1.5s ease-in-out infinite;
}

.indicator-dot.emergency-active {
  background: #ef4444;
}

.indicator-dot.gesture-detected {
  background: #f59e0b;
}

.indicator-dot.identity-error {
  background: #8b5cf6;
}

.indicator-dot.timeout-error {
  background: #ef4444;
}

.indicator-label {
  font-size: 0.75rem;
  color: white;
  font-weight: 500;
}

/* 动画 */
@keyframes emergencyFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes backdropPulse {
  0%, 100% {
    background: rgba(220, 38, 38, 0.2);
  }
  50% {
    background: rgba(220, 38, 38, 0.3);
  }
}

@keyframes emergencyPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 25px 50px rgba(220, 38, 38, 0.4);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 30px 60px rgba(220, 38, 38, 0.5);
  }
}

@keyframes iconShake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-2px);
  }
  75% {
    transform: translateX(2px);
  }
}

@keyframes statusBlink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes indicatorPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .emergency-content {
    margin: 1rem;
    padding: 2rem;
    max-width: calc(100vw - 2rem);
  }
  
  .emergency-title {
    font-size: 1.5rem;
  }
  
  .emergency-indicators {
    top: 1rem;
    right: 1rem;
  }
  
  .icon-container {
    width: 80px;
    height: 80px;
  }
  
  .emergency-svg {
    width: 40px;
    height: 40px;
  }
}
</style>