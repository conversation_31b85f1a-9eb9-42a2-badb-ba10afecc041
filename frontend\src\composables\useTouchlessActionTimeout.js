/**
 * 无接触式动作超时检测系统
 * 基于时间维度检测患者是否无法完成动作，并自动跳过困难动作
 */

import { ref, computed } from "vue";

// 超时配置
const TIMEOUT_CONFIG = {
  maxAttemptTime: 20000, // 20秒总超时
  progressStagnation: 15000, // 10秒无进展检测
  repeatedFailure: 3, // 连续3次失败自动跳过
  skipCooldown: 5000, // 跳过后5秒冷却时间
  warningTime: 10000, // 60秒时给出警告
  progressCheckInterval: 2000, // 每2秒检查一次进展
};

export function useTouchlessActionTimeout() {
  // 检测状态
  const isDetectionActive = ref(false);
  const currentAction = ref(null);
  const actionStartTime = ref(null);
  const lastProgressTime = ref(null);
  const lastProgressState = ref(null);
  const failureCount = ref(0);
  const hasWarned = ref(false);
  const progressCheckTimer = ref(null);

  // 回调函数
  const callbacks = ref({
    onTimeout: null,
    onStagnation: null,
    onWarning: null,
    onSkip: null,
  });

  // 计算属性
  const elapsedTime = computed(() => {
    if (!actionStartTime.value) return 0;
    return Date.now() - actionStartTime.value;
  });

  const stagnationTime = computed(() => {
    if (!lastProgressTime.value) return elapsedTime.value;
    return Date.now() - lastProgressTime.value;
  });

  const isStagnant = computed(() => {
    return stagnationTime.value > TIMEOUT_CONFIG.progressStagnation;
  });

  const shouldWarn = computed(() => {
    return elapsedTime.value > TIMEOUT_CONFIG.warningTime && !hasWarned.value;
  });

  const shouldTimeout = computed(() => {
    return elapsedTime.value > TIMEOUT_CONFIG.maxAttemptTime;
  });

  /**
   * 启动超时检测
   * @param {Object} action - 当前动作对象
   * @param {Object} options - 回调函数配置
   */
  const startDetection = (action, options = {}) => {
    if (isDetectionActive.value) {
      console.warn("[TouchlessTimeout] 检测已在运行，先停止当前检测");
      stopDetection();
    }

    console.log(`[TouchlessTimeout] 开始检测动作: ${action.action_name}`);

    // 初始化状态
    currentAction.value = action;
    actionStartTime.value = Date.now();
    lastProgressTime.value = Date.now();
    lastProgressState.value = null;
    hasWarned.value = false;
    isDetectionActive.value = true;

    // 设置回调函数
    callbacks.value = {
      onTimeout: options.onTimeout || (() => {}),
      onStagnation: options.onStagnation || (() => {}),
      onWarning: options.onWarning || (() => {}),
      onSkip: options.onSkip || (() => {}),
    };

    // 启动进展检查定时器
    startProgressCheck();
  };

  /**
   * 停止超时检测
   */
  const stopDetection = () => {
    if (!isDetectionActive.value) return;

    console.log("[TouchlessTimeout] 停止超时检测");

    // 清理定时器
    if (progressCheckTimer.value) {
      clearInterval(progressCheckTimer.value);
      progressCheckTimer.value = null;
    }

    // 重置状态
    isDetectionActive.value = false;
    currentAction.value = null;
    actionStartTime.value = null;
    lastProgressTime.value = null;
    lastProgressState.value = null;
    hasWarned.value = false;

    // 清空回调
    callbacks.value = {
      onTimeout: null,
      onStagnation: null,
      onWarning: null,
      onSkip: null,
    };
  };
  /**
   * 更新动作进展
   * @param {string} currentState - 当前动作状态
   * @param {number} currentScore - 当前分数
   */
  const updateProgress = (currentState, currentScore = 0) => {
    if (!isDetectionActive.value) return;

    // 检查是否有实际进展
    const hasProgress = checkForProgress(currentState, currentScore);

    if (hasProgress) {
      console.log(
        `[TouchlessTimeout] 检测到进展: ${currentState}, 分数: ${currentScore}`
      );
      lastProgressTime.value = Date.now();
      lastProgressState.value = { state: currentState, score: currentScore };

      // 重置警告状态（如果用户重新开始有进展）
      if (
        hasWarned.value &&
        elapsedTime.value < TIMEOUT_CONFIG.maxAttemptTime * 0.8
      ) {
        hasWarned.value = false;
      }
    }
  };

  /**
   * 检查是否有实际进展
   */
  const checkForProgress = (currentState, currentScore) => {
    if (!lastProgressState.value) return true; // 首次状态更新算作进展

    const lastState = lastProgressState.value.state;
    const lastScore = lastProgressState.value.score;

    // 状态变化或分数提升都算作进展
    return currentState !== lastState || currentScore > lastScore;
  };

  /**
   * 启动进展检查定时器
   */
  const startProgressCheck = () => {
    progressCheckTimer.value = setInterval(() => {
      if (!isDetectionActive.value) return;

      // 检查是否需要发出警告
      if (shouldWarn.value) {
        handleWarning();
      }

      // 检查是否需要超时处理
      if (shouldTimeout.value) {
        handleTimeout();
      }

      // 检查是否停滞
      if (isStagnant.value && !hasWarned.value) {
        handleStagnation();
      }
    }, TIMEOUT_CONFIG.progressCheckInterval);
  };

  /**
   * 处理警告
   */
  const handleWarning = () => {
    if (hasWarned.value) return;

    hasWarned.value = true;
    const remainingTime = Math.ceil(
      (TIMEOUT_CONFIG.maxAttemptTime - elapsedTime.value) / 1000
    );

    console.log(
      `[TouchlessTimeout] 发出超时警告，剩余时间: ${remainingTime}秒`
    );

    callbacks.value.onWarning?.({
      type: "timeout_warning",
      remainingTime,
      message: `动作即将超时，剩余 ${remainingTime} 秒`,
    });
  };

  /**
   * 处理停滞
   */
  const handleStagnation = () => {
    console.log(`[TouchlessTimeout] 检测到动作停滞: ${stagnationTime.value}ms`);

    // 记录停滞信息到控制台（移除了不存在的 exceptionStore 调用）
    console.log(`[TouchlessTimeout] 停滞详情:`, {
      action: currentAction.value?.action_name,
      stagnationTime: stagnationTime.value,
      elapsedTime: elapsedTime.value,
    });

    callbacks.value.onStagnation?.({
      type: "stagnation",
      stagnationTime: stagnationTime.value,
      message: "动作进展缓慢，请继续尝试或等待自动跳过",
    });
  };

  /**
   * 处理超时
   */
  const handleTimeout = () => {
    console.log(
      `[TouchlessTimeout] 动作超时，自动跳过: ${currentAction.value?.action_name}`
    );

    // 增加失败计数
    failureCount.value++;

    // 停止检测
    stopDetection();

    // 触发跳过回调
    callbacks.value.onTimeout?.({
      type: "timeout",
      action: currentAction.value,
      elapsedTime: elapsedTime.value,
      reason: "max_attempt_time_exceeded",
    });
  };

 

  return {
    // 状态
    isDetectionActive,
    elapsedTime,
    stagnationTime,
    isStagnant,
    shouldWarn,
    shouldTimeout,
    failureCount,

    // 方法
    startDetection,
    stopDetection,
    updateProgress,
    getDetectionStatus: () => ({
      isActive: isDetectionActive.value,
      action: currentAction.value,
      elapsedTime: elapsedTime.value,
      stagnationTime: stagnationTime.value,
      isStagnant: isStagnant.value,
      hasWarned: hasWarned.value,
      failureCount: failureCount.value,
    }),

    // 配置
    TIMEOUT_CONFIG,
  };
}
