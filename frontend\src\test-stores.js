// /**
//  * 模块化Store测试脚本
//  * 用于验证新的store架构是否正常工作
//  */

// // 导入所有新的store
// import { useConnectionStore } from './stores/connection'
// import { usePatientStore } from './stores/patient'
// import { useTrainingStore } from './stores/training'
// import { useWorkflowStore } from './stores/workflow'
// import { useNotificationStore } from './stores/notification'

// // 导入composables
// import { useStateTransition } from './composables/useStateTransition'

// /**
//  * 测试所有store的基本功能
//  */
// export function testStores() {
//   console.log('🧪 开始测试模块化Store架构...')
  
//   try {
//     // 测试ConnectionStore
//     console.log('📡 测试ConnectionStore...')
//     const connectionStore = useConnectionStore()
//     console.log('✅ ConnectionStore初始化成功')
//     console.log('   - isConnected:', connectionStore.isConnected)
//     console.log('   - connectionStatus:', connectionStore.connectionStatus)
    
//     // 测试PatientStore
//     console.log('👤 测试PatientStore...')
//     const patientStore = usePatientStore()
//     console.log('✅ PatientStore初始化成功')
//     console.log('   - isUserLoggedIn:', patientStore.isUserLoggedIn)
//     console.log('   - userInfo:', patientStore.userInfo)
    
//     // 测试TrainingStore
//     console.log('🏃 测试TrainingStore...')
//     const trainingStore = useTrainingStore()
//     console.log('✅ TrainingStore初始化成功')
//     console.log('   - actionList length:', trainingStore.actionList.length)
//     console.log('   - currentAction:', trainingStore.currentAction)
    
//     // 测试WorkflowStore
//     console.log('⚙️ 测试WorkflowStore...')
//     const workflowStore = useWorkflowStore()
//     console.log('✅ WorkflowStore初始化成功')
//     console.log('   - currentState:', workflowStore.currentState)
//     console.log('   - isPaused:', workflowStore.isPaused)
    
//     // 测试NotificationStore
//     console.log('📢 测试NotificationStore...')
//     const notificationStore = useNotificationStore()
//     console.log('✅ NotificationStore初始化成功')
//     console.log('   - hasActiveNotification:', notificationStore.hasActiveNotification)
//     console.log('   - notificationCount:', notificationStore.notificationCount)
    
//     // 测试Composables
//     console.log('🔧 测试Composables...')



//     // 测试反馈和评分系统
//     console.log('🔄 测试useStateTransition...')
//     const stateTransition = useStateTransition()
//     console.log('✅ useStateTransition初始化成功')
//     console.log('   - isAutoTransitionEnabled:', stateTransition.isAutoTransitionEnabled)


//     console.log('🎉 所有Store和Composables测试通过！')
//     return true
    
//   } catch (error) {
//     console.error('❌ Store测试失败:', error)
//     return false
//   }
// }

// /**
//  * 测试store之间的数据流
//  */
// export function testStoreDataFlow() {
//   console.log('🔄 测试Store数据流...')
  
//   try {
//     const connectionStore = useConnectionStore()
//     const patientStore = usePatientStore()
//     const trainingStore = useTrainingStore()
//     const workflowStore = useWorkflowStore()
//     const notificationStore = useNotificationStore()
    
//     // 模拟连接成功
//     console.log('📡 模拟连接成功...')
//     connectionStore.setConnectionStatus(true)
    
//     // 模拟患者校验成功
//     console.log('👤 模拟患者校验成功...')
//     patientStore.handleValidationSuccess('TEST_PATIENT_001')
    
//     // 初始化训练动作
//     console.log('🏃 初始化训练动作...')
//     trainingStore.initializeActions()
    
//     // 显示通知
//     console.log('📢 显示测试通知...')
//     notificationStore.showSuccess('测试通知消息', 1000)
    
//     // 状态转换测试
//     console.log('⚙️ 测试状态转换...')
//     const canTransition = workflowStore.canTransitionTo('introduction')
//     console.log('   - 可以转换到introduction:', canTransition)
    
//     console.log('✅ 数据流测试完成！')
//     console.log('   - 连接状态:', connectionStore.isConnected)
//     console.log('   - 用户登录状态:', patientStore.isUserLoggedIn)
//     console.log('   - 动作列表长度:', trainingStore.actionList.length)
//     console.log('   - 当前状态:', workflowStore.currentState)
//     console.log('   - 活跃通知:', notificationStore.hasActiveNotification)
    
//     return true
    
//   } catch (error) {
//     console.error('❌ 数据流测试失败:', error)
//     return false
//   }
// }

// /**
//  * 运行所有测试
//  */
// export function runAllTests() {
//   console.log('🚀 开始运行所有Store测试...')
  
//   const basicTest = testStores()
//   const dataFlowTest = testStoreDataFlow()
  
//   if (basicTest && dataFlowTest) {
//     console.log('🎉 所有测试通过！模块化Store架构工作正常。')
//     return true
//   } else {
//     console.log('❌ 部分测试失败，请检查Store实现。')
//     return false
//   }
// }

// // 在开发环境下暴露测试函数到全局
// if (import.meta.env.DEV) {
//   window.testStores = testStores
//   window.testStoreDataFlow = testStoreDataFlow
//   window.runAllTests = runAllTests
  
//   console.log('🧪 Store测试函数已暴露到全局:')
//   console.log('   - window.testStores()')
//   console.log('   - window.testStoreDataFlow()')
//   console.log('   - window.runAllTests()')
// }
